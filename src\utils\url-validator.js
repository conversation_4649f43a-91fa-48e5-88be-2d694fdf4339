/**
 * URL validation utilities for Downloader Pro
 */

class URLValidator {
    constructor() {
        // Supported platform patterns
        this.patterns = {
            youtube: [
                /^https?:\/\/(www\.)?(youtube\.com|youtu\.be)\/.+/i,
                /^https?:\/\/(www\.)?m\.youtube\.com\/.+/i
            ],
            facebook: [
                /^https?:\/\/(www\.)?(facebook\.com|fb\.watch)\/.+/i,
                /^https?:\/\/(www\.)?m\.facebook\.com\/.+/i
            ],
            instagram: [
                /^https?:\/\/(www\.)?instagram\.com\/.+/i
            ],
            tiktok: [
                /^https?:\/\/(www\.)?(tiktok\.com|vm\.tiktok\.com)\/.+/i
            ],
            twitter: [
                /^https?:\/\/(www\.)?(twitter\.com|x\.com)\/.+/i,
                /^https?:\/\/(www\.)?t\.co\/.+/i
            ],
            pinterest: [
                /^https?:\/\/(www\.)?pinterest\.com\/pin\/\d+\/?/i,
                /^https?:\/\/(www\.)?pinterest\.com\/[^\/]+\/[^\/]+\/?/i,
                /^https?:\/\/(www\.)?pinterest\.com\/[^\/]+\/?/i,
                /^https?:\/\/pin\.it\/[a-zA-Z0-9]+\/?/i,
                /^https?:\/\/(www\.)?pinterest\.[a-z]{2,3}\/pin\/\d+\/?/i,
                /^https?:\/\/(www\.)?pinterest\.[a-z]{2,3}\/[^\/]+\/[^\/]+\/?/i,
                /^https?:\/\/(www\.)?pinterest\.[a-z]{2,3}\/[^\/]+\/?/i,
                /^https?:\/\/(www\.)?pinterest\.co\.[a-z]{2}\/pin\/\d+\/?/i,
                /^https?:\/\/(www\.)?pinterest\.co\.[a-z]{2}\/[^\/]+\/[^\/]+\/?/i,
                /^https?:\/\/(www\.)?pinterest\.co\.[a-z]{2}\/[^\/]+\/?/i
            ]
        };
    }

    /**
     * Check if a URL is valid
     * @param {string} url - The URL to validate
     * @returns {boolean} - True if valid, false otherwise
     */
    isValidUrl(url) {
        if (!url || typeof url !== 'string') {
            return false;
        }

        try {
            new URL(url);
            return true;
        } catch {
            return false;
        }
    }

    /**
     * Check if a URL is from a supported platform
     * @param {string} url - The URL to check
     * @returns {string|null} - Platform name if supported, null otherwise
     */
    getSupportedPlatform(url) {
        if (!this.isValidUrl(url)) {
            return null;
        }

        for (const [platform, patterns] of Object.entries(this.patterns)) {
            for (const pattern of patterns) {
                if (pattern.test(url)) {
                    return platform;
                }
            }
        }

        return null;
    }

    /**
     * Check if a URL is from a supported platform
     * @param {string} url - The URL to check
     * @returns {boolean} - True if supported, false otherwise
     */
    isSupportedUrl(url) {
        return this.getSupportedPlatform(url) !== null;
    }

    /**
     * Normalize a URL (remove tracking parameters, etc.)
     * @param {string} url - The URL to normalize
     * @returns {string} - Normalized URL
     */
    normalizeUrl(url) {
        if (!this.isValidUrl(url)) {
            return url;
        }

        try {
            const urlObj = new URL(url);
            
            // Remove common tracking parameters
            const trackingParams = [
                'utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content',
                'fbclid', 'gclid', 'ref', 'source', 'campaign'
            ];
            
            trackingParams.forEach(param => {
                urlObj.searchParams.delete(param);
            });

            // Platform-specific normalizations
            const platform = this.getSupportedPlatform(url);
            
            if (platform === 'youtube') {
                // Keep only essential YouTube parameters
                const keepParams = ['v', 't', 'list', 'index'];
                const newParams = new URLSearchParams();

                keepParams.forEach(param => {
                    if (urlObj.searchParams.has(param)) {
                        newParams.set(param, urlObj.searchParams.get(param));
                    }
                });

                urlObj.search = newParams.toString();
            } else if (platform === 'pinterest') {
                // Pinterest URLs are generally clean, just remove tracking params
                // Keep essential parameters if any
                const keepParams = [];
                const newParams = new URLSearchParams();

                keepParams.forEach(param => {
                    if (urlObj.searchParams.has(param)) {
                        newParams.set(param, urlObj.searchParams.get(param));
                    }
                });

                urlObj.search = newParams.toString();
            }

            return urlObj.toString();
        } catch {
            return url;
        }
    }

    /**
     * Extract video ID from supported platforms
     * @param {string} url - The URL to extract ID from
     * @returns {string|null} - Video ID if found, null otherwise
     */
    extractVideoId(url) {
        const platform = this.getSupportedPlatform(url);
        if (!platform) return null;

        try {
            const urlObj = new URL(url);

            switch (platform) {
                case 'youtube':
                    // YouTube video ID extraction
                    if (urlObj.hostname.includes('youtu.be')) {
                        return urlObj.pathname.slice(1);
                    } else if (urlObj.searchParams.has('v')) {
                        return urlObj.searchParams.get('v');
                    }
                    break;

                case 'facebook':
                    // Facebook video ID is more complex, return pathname
                    return urlObj.pathname;

                case 'instagram':
                    // Instagram post ID from pathname
                    const instagramMatch = urlObj.pathname.match(/\/p\/([^\/]+)/);
                    return instagramMatch ? instagramMatch[1] : null;

                case 'tiktok':
                    // TikTok video ID from pathname
                    const tiktokMatch = urlObj.pathname.match(/\/video\/(\d+)/);
                    return tiktokMatch ? tiktokMatch[1] : null;

                case 'twitter':
                    // Twitter status ID from pathname
                    const twitterMatch = urlObj.pathname.match(/\/status\/(\d+)/);
                    return twitterMatch ? twitterMatch[1] : null;

                case 'pinterest':
                    // Pinterest pin ID extraction
                    if (urlObj.hostname.includes('pin.it')) {
                        // Short URL format: https://pin.it/abc123
                        return urlObj.pathname.slice(1);
                    } else {
                        // Regular Pinterest URL: https://pinterest.com/pin/123456789/
                        const pinterestMatch = urlObj.pathname.match(/\/pin\/(\d+)/);
                        return pinterestMatch ? pinterestMatch[1] : null;
                    }
            }
        } catch {
            return null;
        }

        return null;
    }

    /**
     * Get platform display name
     * @param {string} platform - Platform identifier
     * @returns {string} - Display name
     */
    getPlatformDisplayName(platform) {
        const displayNames = {
            youtube: 'YouTube',
            facebook: 'Facebook',
            instagram: 'Instagram',
            tiktok: 'TikTok',
            twitter: 'Twitter/X',
            pinterest: 'Pinterest'
        };

        return displayNames[platform] || platform;
    }

    /**
     * Get all supported platforms
     * @returns {Array<string>} - Array of platform names
     */
    getSupportedPlatforms() {
        return Object.keys(this.patterns);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = URLValidator;
} else if (typeof window !== 'undefined') {
    window.URLValidator = URLValidator;
}
