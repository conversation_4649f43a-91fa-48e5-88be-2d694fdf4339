// Main application logic
class DownloaderApp {
    constructor() {
        this.currentUrl = '';
        this.videoInfo = null;
        this.settings = {};
        this.downloads = new Map();
        
        this.init();
    }

    async init() {
        await this.initializeI18n();
        await this.loadSettings();
        this.setupEventListeners();
        this.setupMenuHandlers();
        this.setupClipboardMonitoring();
        this.updateAppVersion();
    }

    async initializeI18n() {
        try {
            if (window.i18nManager) {
                await window.i18nManager.init();
                await window.i18nManager.loadLanguagePreference();

                // Listen for language changes
                window.addEventListener('languageChanged', () => {
                    this.updateTranslatedContent();
                });

                console.log('i18n initialized successfully');
            }
        } catch (error) {
            console.error('Failed to initialize i18n:', error);
        }
    }

    updateTranslatedContent() {
        // Update dynamic content that isn't handled by data-i18n attributes
        this.updateAppVersion();

        // Update any dynamically generated content
        if (this.videoInfo) {
            this.updateVideoPreview();
        }

        // Update notification messages if any are currently shown
        const notifications = document.querySelectorAll('.notification');
        notifications.forEach(notification => {
            // Re-translate notification content if it has a translation key
            const messageElement = notification.querySelector('.notification-message');
            if (messageElement && messageElement.dataset.i18nKey) {
                messageElement.textContent = window.t(messageElement.dataset.i18nKey);
            }
        });
    }

    async loadSettings() {
        try {
            this.settings = await window.electronAPI.getSettings() || {
                theme: 'light',
                autoClipboard: false,
                defaultQuality: 'best',
                defaultFormat: 'auto',
                defaultDestination: 'downloads',
                customDestination: ''
            };
            
            // Apply theme
            document.body.className = `theme-${this.settings.theme}`;

            // Apply auto clipboard setting
            document.getElementById('auto-paste-toggle').checked = this.settings.autoClipboard;

            // Apply default download settings to dropdowns
            this.applyDefaultDownloadSettings();
            
        } catch (error) {
            console.error('Failed to load settings:', error);
            this.showNotification('Error loading settings', 'error');
        }
    }

    applyDefaultDownloadSettings() {
        // Apply default settings to download option dropdowns
        try {
            const qualitySelect = document.getElementById('quality-select');
            const formatSelect = document.getElementById('format-select');
            const destinationSelect = document.getElementById('destination-select');

            if (qualitySelect && this.settings.defaultQuality) {
                qualitySelect.value = this.settings.defaultQuality;
                console.log('Applied default quality:', this.settings.defaultQuality);
            }

            if (formatSelect && this.settings.defaultFormat) {
                formatSelect.value = this.settings.defaultFormat;
                console.log('Applied default format:', this.settings.defaultFormat);
            }

            if (destinationSelect && this.settings.defaultDestination) {
                destinationSelect.value = this.settings.defaultDestination;
                console.log('Applied default destination:', this.settings.defaultDestination);
            }
        } catch (error) {
            console.error('Failed to apply default download settings:', error);
        }
    }

    setupEventListeners() {
        // URL input handling
        const urlInput = document.getElementById('url-input');
        const pasteButton = document.getElementById('paste-button');
        const downloadButton = document.getElementById('download-button');
        
        urlInput.addEventListener('input', (e) => this.handleUrlInput(e.target.value));
        urlInput.addEventListener('paste', (e) => {
            setTimeout(() => this.handleUrlInput(e.target.value), 10);
        });
        
        pasteButton.addEventListener('click', () => this.pasteFromClipboard());
        downloadButton.addEventListener('click', () => this.startDownload());
        
        // Platform icons
        document.querySelectorAll('.platform-icon').forEach(icon => {
            icon.addEventListener('click', (e) => {
                const platform = e.currentTarget.dataset.platform;
                this.openPlatformSearch(platform);
            });
        });
        
        // Theme toggle
        document.getElementById('theme-toggle').addEventListener('click', () => {
            this.toggleTheme();
        });
        
        // Settings button
        document.getElementById('settings-button').addEventListener('click', () => {
            this.openSettings();
        });
        
        // Download controls
        document.getElementById('clear-completed').addEventListener('click', () => {
            this.clearCompletedDownloads();
        });
        
        document.getElementById('pause-all').addEventListener('click', () => {
            this.pauseAllDownloads();
        });
        
        // Modal handling
        this.setupModalHandlers();

        // Drag and drop support
        this.setupDragAndDrop();

        // Quick actions menu
        this.setupQuickActions();

        // Keyboard shortcuts
        this.setupKeyboardShortcuts();

        // Download scheduler
        this.setupDownloadScheduler();

        // Download templates/presets
        this.setupDownloadTemplates();

        // Smart notifications system
        this.setupSmartNotifications();

        // Advanced queue management
        this.setupAdvancedQueueManagement();

        // User onboarding experience
        this.setupUserOnboarding();

        // Load saved scheduled downloads
        this.loadScheduledDownloads();
    }

    setupModalHandlers() {
        // Close modals when clicking outside or on close button
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal.id);
                }
            });
        });
        
        document.querySelectorAll('.modal-close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                const modal = e.target.closest('.modal');
                this.closeModal(modal.id);
            });
        });
        
        // ESC key to close modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal:not(.hidden)');
                if (openModal) {
                    this.closeModal(openModal.id);
                }
            }
        });
    }

    setupMenuHandlers() {
        // Handle menu events from main process
        window.electronAPI.onMenuNewDownload(() => {
            document.getElementById('url-input').focus();
        });
        
        window.electronAPI.onMenuSettings(() => {
            this.openSettings();
        });
        
        window.electronAPI.onMenuAbout(() => {
            this.openAbout();
        });
    }

    setupDragAndDrop() {
        const dropZone = document.getElementById('app');
        const urlInput = document.getElementById('url-input');

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.preventDefaults, false);
            document.body.addEventListener(eventName, this.preventDefaults, false);
        });

        // Highlight drop zone when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => this.highlight(dropZone), false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, () => this.unhighlight(dropZone), false);
        });

        // Handle dropped files/text
        dropZone.addEventListener('drop', (e) => this.handleDrop(e), false);
    }

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    highlight(element) {
        element.classList.add('drag-over');
        this.showNotification('Drop URLs or text files here to download', 'info');
    }

    unhighlight(element) {
        element.classList.remove('drag-over');
    }

    async handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;
        const text = dt.getData('text');

        if (files.length > 0) {
            // Handle file drops
            await this.handleFileDrops(files);
        } else if (text) {
            // Handle text drops (URLs)
            await this.handleTextDrop(text);
        }
    }

    async handleFileDrops(files) {
        for (let file of files) {
            if (file.type === 'text/plain' || file.name.endsWith('.txt')) {
                try {
                    const content = await this.readFileAsText(file);
                    await this.processDroppedText(content);
                } catch (error) {
                    this.showNotification(`Failed to read file: ${file.name}`, 'error');
                }
            } else {
                this.showNotification(`Unsupported file type: ${file.name}`, 'warning');
            }
        }
    }

    async handleTextDrop(text) {
        await this.processDroppedText(text);
    }

    async processDroppedText(text) {
        const urls = this.extractUrlsFromText(text);

        if (urls.length === 0) {
            this.showNotification('No valid URLs found in dropped content', 'warning');
            return;
        }

        if (urls.length === 1) {
            // Single URL - populate input field
            const urlInput = document.getElementById('url-input');
            urlInput.value = urls[0];
            await this.handleUrlInput(urls[0]);
            this.showNotification('URL added successfully!', 'success');
        } else {
            // Multiple URLs - open batch download modal
            this.showBatchDownloadModal(urls);
            this.showNotification(`Found ${urls.length} URLs - opening batch download`, 'success');
        }
    }

    extractUrlsFromText(text) {
        const urlRegex = /https?:\/\/[^\s]+/g;
        const foundUrls = text.match(urlRegex) || [];

        // Filter for supported platforms
        return foundUrls.filter(url => this.isValidUrl(url));
    }

    readFileAsText(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = e => resolve(e.target.result);
            reader.onerror = e => reject(e);
            reader.readAsText(file);
        });
    }

    setupClipboardMonitoring() {
        // Initialize clipboard monitoring state
        this.lastClipboardUrl = '';
        this.clipboardInterval = null;

        // Start monitoring if enabled in settings
        if (this.settings.autoClipboard) {
            this.startClipboardMonitoring();
        }

        // Handle toggle changes
        document.getElementById('auto-paste-toggle').addEventListener('change', (e) => {
            this.settings.autoClipboard = e.target.checked;
            this.saveSettings();

            if (e.target.checked) {
                this.startClipboardMonitoring();
                this.showNotification('Auto-detect clipboard enabled', 'success');
            } else {
                this.stopClipboardMonitoring();
                this.showNotification('Auto-detect clipboard disabled', 'info');
            }
        });
    }

    startClipboardMonitoring() {
        // Stop any existing monitoring
        this.stopClipboardMonitoring();

        console.log('Starting clipboard monitoring...');

        this.clipboardInterval = setInterval(async () => {
            try {
                const clipboardText = await window.clipboardAPI.readText();

                // Check if clipboard has new valid URL
                if (clipboardText &&
                    clipboardText.trim() !== this.lastClipboardUrl &&
                    this.isValidUrl(clipboardText.trim())) {

                    this.lastClipboardUrl = clipboardText.trim();
                    const urlInput = document.getElementById('url-input');

                    // Only auto-paste if input is empty or user hasn't started typing
                    if (!urlInput.value.trim() || urlInput.value === this.lastClipboardUrl) {
                        urlInput.value = this.lastClipboardUrl;
                        await this.handleUrlInput(this.lastClipboardUrl);
                        this.showNotification('Video URL detected and pasted automatically!', 'success');
                    }
                }
            } catch (error) {
                console.log('Clipboard monitoring error:', error.message);
                // Ignore clipboard access errors - they're common and expected
            }
        }, 1500); // Check every 1.5 seconds to be less aggressive
    }

    stopClipboardMonitoring() {
        if (this.clipboardInterval) {
            clearInterval(this.clipboardInterval);
            this.clipboardInterval = null;
            console.log('Clipboard monitoring stopped');
        }
    }

    async pasteFromClipboard() {
        try {
            const clipboardText = await window.clipboardAPI.readText();
            if (clipboardText) {
                const urlInput = document.getElementById('url-input');
                urlInput.value = clipboardText.trim();
                await this.handleUrlInput(clipboardText.trim());
                this.showNotification('Pasted from clipboard', 'success');
            } else {
                this.showNotification('Clipboard is empty', 'warning');
            }
        } catch (error) {
            console.error('Clipboard paste error:', error);
            this.showNotification('Failed to read clipboard', 'error');
        }
    }

    isValidUrl(string) {
        try {
            const url = new URL(string);
            const supportedDomains = [
                'youtube.com', 'youtu.be', 'facebook.com', 'fb.watch',
                'instagram.com', 'tiktok.com', 'twitter.com', 'x.com',
                'pinterest.com', 'pin.it'
            ];
            return supportedDomains.some(domain => url.hostname.includes(domain));
        } catch {
            return false;
        }
    }

    async handleUrlInput(url) {
        console.log('handleUrlInput called with:', url);
        this.currentUrl = url.trim();
        const downloadButton = document.getElementById('download-button');

        if (!this.currentUrl) {
            console.log('Empty URL, disabling download button');
            downloadButton.disabled = true;
            this.hideVideoPreview();
            return;
        }

        console.log('Checking if URL is valid:', this.currentUrl);
        if (this.isValidUrl(this.currentUrl)) {
            console.log('URL is valid, enabling download button and loading video info');
            downloadButton.disabled = false;
            await this.loadVideoInfo(this.currentUrl);
        } else {
            console.log('URL is not valid, disabling download button');
            downloadButton.disabled = true;
            this.hideVideoPreview();
        }
    }

    async loadVideoInfo(url) {
        try {
            console.log('Loading video info for URL:', url);
            this.showLoadingState();
            this.videoInfo = await window.electronAPI.getVideoInfo(url);
            console.log('Video info loaded:', this.videoInfo);
            this.displayVideoPreview(this.videoInfo);
            this.showDownloadOptions();

            // Generate smart suggestions (with error handling)
            try {
                this.generateSmartSuggestions(this.videoInfo);
            } catch (error) {
                console.error('Error generating smart suggestions:', error);
                // Don't let suggestions break the main functionality
            }
        } catch (error) {
            console.error('Failed to load video info:', error);
            this.showNotification('Failed to load video information', 'error');
            this.hideVideoPreview();
        } finally {
            this.hideLoadingState();
        }
    }

    showLoadingState() {
        const downloadButton = document.getElementById('download-button');
        downloadButton.innerHTML = '<div class="loading-spinner"></div><span>Loading...</span>';
        downloadButton.disabled = true;
    }

    hideLoadingState() {
        const downloadButton = document.getElementById('download-button');
        downloadButton.innerHTML = '<span class="icon">⬇️</span><span class="text">Download</span>';
        downloadButton.disabled = false;
    }

    displayVideoPreview(videoInfo) {
        const previewSection = document.getElementById('video-preview');
        const previewImage = document.getElementById('preview-image');
        const previewTitle = document.getElementById('preview-title');
        const previewDescription = document.getElementById('preview-description');
        const previewDuration = document.getElementById('preview-duration');
        const previewUploader = document.getElementById('preview-uploader');
        const previewViews = document.getElementById('preview-views');
        
        previewImage.src = videoInfo.thumbnail || '';
        previewTitle.textContent = videoInfo.title || 'Unknown Title';
        previewDescription.textContent = videoInfo.description || '';
        previewDuration.textContent = this.formatDuration(videoInfo.duration);
        previewUploader.textContent = videoInfo.uploader || '';
        previewViews.textContent = videoInfo.view_count ? `${this.formatNumber(videoInfo.view_count)} views` : '';
        
        previewSection.classList.remove('hidden');
    }

    hideVideoPreview() {
        document.getElementById('video-preview').classList.add('hidden');
        document.getElementById('download-options').classList.add('hidden');
    }

    showDownloadOptions() {
        document.getElementById('download-options').classList.remove('hidden');
    }

    formatDuration(seconds) {
        if (!seconds) return '';
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        const secs = seconds % 60;
        
        if (hours > 0) {
            return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }

    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        }
        if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        }
        return num.toString();
    }

    async startDownload() {
        console.log('startDownload called');
        console.log('currentUrl:', this.currentUrl);
        console.log('videoInfo:', this.videoInfo);

        if (!this.currentUrl || !this.videoInfo) {
            console.error('Missing URL or video info');
            this.showNotification('Please enter a valid video URL first', 'error');
            return;
        }

        const downloadType = document.getElementById('download-type').value;
        const quality = document.getElementById('quality-select').value;
        const format = document.getElementById('format-select').value;
        const destination = await this.getDestinationPath();

        const downloadOptions = {
            url: this.currentUrl,
            type: downloadType,
            quality: quality,
            format: format,
            destination: destination,
            videoInfo: this.videoInfo
        };

        console.log('Download options:', downloadOptions);

        try {
            console.log('Calling electronAPI.startDownload...');
            const downloadId = await window.electronAPI.startDownload(downloadOptions);
            console.log('Download started with ID:', downloadId);

            console.log('Adding download to list...');
            this.addDownloadToList(downloadId, downloadOptions);

            // Use smart notification for download start
            const title = this.videoInfo.title || 'Video Download';
            this.showSmartNotification(
                'Download Started 🚀',
                `${title} - ${quality} quality`,
                'info',
                {
                    tag: `download-start-${downloadId}`,
                    duration: 4000
                }
            );

            // Clear input
            document.getElementById('url-input').value = '';
            this.currentUrl = '';
            this.hideVideoPreview();
            document.getElementById('download-button').disabled = true;

        } catch (error) {
            console.error('Failed to start download:', error);
            this.notifyDownloadError(error.message, this.videoInfo);
        }
    }

    async getDestinationPath() {
        const destinationSelect = document.getElementById('destination-select');
        const selectedDestination = destinationSelect.value;
        
        switch (selectedDestination) {
            case 'downloads':
                return await window.electronAPI.getDownloadsPath();
            case 'videos':
                return await window.electronAPI.getVideosPath();
            case 'custom':
                return this.settings.customDestination || await window.electronAPI.getDownloadsPath();
            default:
                return await window.electronAPI.getDownloadsPath();
        }
    }

    toggleTheme() {
        const currentTheme = this.settings.theme;
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        
        document.body.classList.add('theme-changing');
        document.body.className = `theme-${newTheme}`;
        
        setTimeout(() => {
            document.body.classList.remove('theme-changing');
        }, 300);
        
        this.settings.theme = newTheme;
        this.saveSettings();
        
        // Update theme toggle icon
        const themeToggle = document.getElementById('theme-toggle');
        themeToggle.querySelector('.icon').textContent = newTheme === 'light' ? '🌙' : '☀️';
    }

    async saveSettings() {
        try {
            await window.electronAPI.saveSettings(this.settings);
        } catch (error) {
            console.error('Failed to save settings:', error);
        }
    }

    openSettings() {
        document.getElementById('settings-modal').classList.remove('hidden');
    }

    openAbout() {
        document.getElementById('about-modal').classList.remove('hidden');
    }

    closeModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }

    openPlatformSearch(platform) {
        // Platform-specific search URLs and configurations
        const platformConfig = {
            youtube: {
                name: 'YouTube',
                searchUrl: 'https://www.youtube.com/results?search_query=',
                placeholder: 'Search for videos on YouTube...',
                icon: '../../assets/platforms/youtube.png',
                tips: [
                    'Search for music, tutorials, entertainment',
                    'Use quotes for exact phrases',
                    'Add "HD" or "4K" for quality filters'
                ]
            },
            facebook: {
                name: 'Facebook',
                searchUrl: 'https://www.facebook.com/search/videos/?q=',
                placeholder: 'Search for videos on Facebook...',
                icon: '../../assets/platforms/facebook.png',
                tips: [
                    'Search public videos and posts',
                    'Use hashtags to find trending content',
                    'Search by page or creator name'
                ]
            },
            instagram: {
                name: 'Instagram',
                searchUrl: 'https://www.instagram.com/explore/tags/',
                placeholder: 'Search hashtags on Instagram...',
                icon: '../../assets/platforms/instagram.png',
                tips: [
                    'Search using hashtags without #',
                    'Find trending reels and videos',
                    'Discover content by topic'
                ]
            },
            tiktok: {
                name: 'TikTok',
                searchUrl: 'https://www.tiktok.com/search?q=',
                placeholder: 'Search for videos on TikTok...',
                icon: '../../assets/platforms/tiktok.png',
                tips: [
                    'Search for trending videos',
                    'Use hashtags to find topics',
                    'Search by creator username'
                ]
            },
            twitter: {
                name: 'Twitter/X',
                searchUrl: 'https://twitter.com/search?q=',
                placeholder: 'Search for videos on Twitter/X...',
                icon: '../../assets/platforms/twitter.png',
                tips: [
                    'Search tweets with videos',
                    'Use hashtags and mentions',
                    'Filter by "Videos" in advanced search'
                ]
            },
            pinterest: {
                name: 'Pinterest',
                searchUrl: 'https://www.pinterest.com/search/pins/?q=',
                placeholder: 'Search for videos and pins on Pinterest...',
                icon: '../../assets/platforms/pinterest.png',
                tips: [
                    'Search for video pins and ideas',
                    'Use keywords to find inspiration',
                    'Discover trending video content',
                    'Search by category or topic'
                ]
            }
        };

        const config = platformConfig[platform];
        if (!config) {
            this.showNotification('Platform not supported yet', 'error');
            return;
        }

        this.showPlatformSearchModal(config);
    }

    showPlatformSearchModal(config) {
        // Remove existing modal if present
        const existingModal = document.querySelector('.platform-search-modal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHTML = `
            <div class="platform-search-modal" id="platform-search-modal">
                <div class="modal-overlay" onclick="window.app.closePlatformSearchModal()"></div>
                <div class="modal-content platform-search-content">
                    <div class="modal-header">
                        <div class="platform-info">
                            <img src="${config.icon}" alt="${config.name}" class="platform-modal-icon">
                            <h2>Search ${config.name}</h2>
                        </div>
                        <button class="modal-close" onclick="window.app.closePlatformSearchModal()">×</button>
                    </div>

                    <div class="modal-body">
                        <div class="search-input-container">
                            <input type="text"
                                   id="platform-search-input"
                                   placeholder="${config.placeholder}"
                                   class="platform-search-input">
                            <button id="platform-search-btn" class="platform-search-button">
                                🔍 Search
                            </button>
                        </div>

                        <div class="search-tips">
                            <h4>💡 Search Tips:</h4>
                            <ul>
                                ${config.tips.map(tip => `<li>${tip}</li>`).join('')}
                            </ul>
                        </div>

                        <div class="search-workflow">
                            <h4>📋 How it works:</h4>
                            <ol>
                                <li>Enter your search terms above</li>
                                <li>Click "Search" to open ${config.name} in your browser</li>
                                <li>Copy the URL of any video you want to download</li>
                                <li>Return here and paste the URL to start downloading</li>
                            </ol>
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button class="btn-secondary" onclick="window.app.closePlatformSearchModal()">
                            Cancel
                        </button>
                        <button class="btn-primary" onclick="window.app.enableClipboardMonitoring()">
                            🎯 Enable Auto-Paste
                        </button>
                    </div>
                </div>
            </div>
        `;

        // Add modal to DOM
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add event listeners
        const searchInput = document.getElementById('platform-search-input');
        const searchBtn = document.getElementById('platform-search-btn');

        // Handle search
        const performSearch = () => {
            const query = searchInput.value.trim();
            if (!query) {
                this.showNotification('Please enter a search term', 'error');
                return;
            }

            let searchUrl;
            if (config.name === 'Instagram') {
                // Instagram uses hashtag format
                searchUrl = config.searchUrl + encodeURIComponent(query.replace('#', ''));
            } else {
                searchUrl = config.searchUrl + encodeURIComponent(query);
            }

            // Open search in external browser
            window.electronAPI.openExternal(searchUrl);

            // Show success message
            this.showNotification(`Opening ${config.name} search in browser...`, 'success');

            // Enable clipboard monitoring for easy paste
            this.enableClipboardMonitoring();

            // Close modal after short delay
            setTimeout(() => {
                this.closePlatformSearchModal();
            }, 1500);
        };

        searchBtn.addEventListener('click', performSearch);
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });

        // Handle escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                this.closePlatformSearchModal();
            }
        });

        // Focus on input
        setTimeout(() => searchInput.focus(), 100);
    }

    closePlatformSearchModal() {
        const modal = document.querySelector('.platform-search-modal');
        if (modal) {
            modal.remove();
        }
    }

    enableClipboardMonitoring() {
        // Enable the auto-detect clipboard toggle
        const toggle = document.getElementById('auto-paste-toggle');
        if (toggle && !toggle.checked) {
            toggle.checked = true;
            this.settings.autoClipboard = true;
            this.saveSettings();
            this.startClipboardMonitoring();
            this.showNotification('Auto-detect clipboard enabled! Copy a video URL and it will be detected automatically.', 'info');
        }
    }

    async updateAppVersion() {
        try {
            const version = await window.electronAPI.getAppVersion();
            document.getElementById('app-version').textContent = `Version ${version}`;
        } catch (error) {
            console.error('Failed to get app version:', error);
        }
    }

    showNotification(message, type = 'info', title = '', translationKey = null, translationOptions = {}) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;

        // Handle translation if key is provided
        let displayMessage = message;
        let displayTitle = title;

        if (translationKey && window.t) {
            displayMessage = window.t(translationKey, translationOptions);
        }

        if (title && title.startsWith('i18n:') && window.t) {
            displayTitle = window.t(title.replace('i18n:', ''));
        }

        const messageElement = document.createElement('div');
        messageElement.className = 'notification-message';
        messageElement.textContent = displayMessage;

        // Store translation key for language switching
        if (translationKey) {
            messageElement.dataset.i18nKey = translationKey;
            if (Object.keys(translationOptions).length > 0) {
                messageElement.dataset.i18nOptions = JSON.stringify(translationOptions);
            }
        }

        notification.innerHTML = displayTitle ? `<div class="notification-title">${displayTitle}</div>` : '';
        notification.appendChild(messageElement);

        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // Download management methods - integrated with DownloadManager
    addDownloadToList(downloadId, options) {
        try {
            // Check if download manager is available
            if (!window.downloadManager) {
                console.error('Download manager not available');
                this.showNotification('Download manager not ready. Please try again.', 'error');
                return false;
            }

            // Add download to the download manager
            window.downloadManager.addDownload(downloadId, options);

            // Show success notification
            this.showNotification('Download added to queue', 'success');

            // Switch to downloads tab if available
            this.switchToDownloadsTab();

            return true;
        } catch (error) {
            console.error('Error adding download to list:', error);
            this.showNotification('Failed to add download to list', 'error');
            return false;
        }
    }

    clearCompletedDownloads() {
        try {
            if (!window.downloadManager) {
                this.showNotification('Download manager not available', 'error');
                return;
            }

            // Get all completed downloads
            const completedDownloads = [];
            window.downloadManager.downloads.forEach((download, id) => {
                if (download.status === 'completed' || download.status === 'error' || download.status === 'cancelled') {
                    completedDownloads.push(id);
                }
            });

            if (completedDownloads.length === 0) {
                this.showNotification('No completed downloads to clear', 'info');
                return;
            }

            // Remove completed downloads
            completedDownloads.forEach(id => {
                const downloadElement = document.querySelector(`[data-download-id="${id}"]`);
                if (downloadElement) {
                    downloadElement.remove();
                }
                window.downloadManager.downloads.delete(id);
            });

            // Update empty state
            if (window.downloadManager.updateEmptyState) {
                window.downloadManager.updateEmptyState();
            }

            this.showNotification(`Cleared ${completedDownloads.length} completed download(s)`, 'success');
        } catch (error) {
            console.error('Error clearing completed downloads:', error);
            this.showNotification('Failed to clear completed downloads', 'error');
        }
    }

    pauseAllDownloads() {
        try {
            if (!window.downloadManager) {
                this.showNotification('Download manager not available', 'error');
                return;
            }

            // Get all active downloads
            const activeDownloads = [];
            window.downloadManager.downloads.forEach((download, id) => {
                if (download.status === 'downloading' || download.status === 'pending') {
                    activeDownloads.push(id);
                }
            });

            if (activeDownloads.length === 0) {
                this.showNotification('No active downloads to pause', 'info');
                return;
            }

            // Pause all active downloads
            let pausedCount = 0;
            const pausePromises = activeDownloads.map(async (id) => {
                try {
                    await window.electronAPI.pauseDownload(id);
                    pausedCount++;
                } catch (error) {
                    console.error(`Failed to pause download ${id}:`, error);
                }
            });

            Promise.all(pausePromises).then(() => {
                if (pausedCount > 0) {
                    this.showNotification(`Paused ${pausedCount} download(s)`, 'success');
                } else {
                    this.showNotification('Failed to pause downloads', 'error');
                }
            });

        } catch (error) {
            console.error('Error pausing all downloads:', error);
            this.showNotification('Failed to pause downloads', 'error');
        }
    }

    resumeAllDownloads() {
        try {
            if (!window.downloadManager) {
                this.showNotification('Download manager not available', 'error');
                return;
            }

            // Get all paused downloads
            const pausedDownloads = [];
            window.downloadManager.downloads.forEach((download, id) => {
                if (download.status === 'paused') {
                    pausedDownloads.push(id);
                }
            });

            if (pausedDownloads.length === 0) {
                this.showNotification('No paused downloads to resume', 'info');
                return;
            }

            // Resume all paused downloads
            let resumedCount = 0;
            const resumePromises = pausedDownloads.map(async (id) => {
                try {
                    await window.electronAPI.resumeDownload(id);
                    resumedCount++;
                } catch (error) {
                    console.error(`Failed to resume download ${id}:`, error);
                }
            });

            Promise.all(resumePromises).then(() => {
                if (resumedCount > 0) {
                    this.showNotification(`Resumed ${resumedCount} download(s)`, 'success');
                } else {
                    this.showNotification('Failed to resume downloads', 'error');
                }
            });

        } catch (error) {
            console.error('Error resuming all downloads:', error);
            this.showNotification('Failed to resume downloads', 'error');
        }
    }

    cancelAllDownloads() {
        try {
            if (!window.downloadManager) {
                this.showNotification('Download manager not available', 'error');
                return;
            }

            // Get all active downloads (downloading, pending, paused)
            const activeDownloads = [];
            window.downloadManager.downloads.forEach((download, id) => {
                if (['downloading', 'pending', 'paused'].includes(download.status)) {
                    activeDownloads.push(id);
                }
            });

            if (activeDownloads.length === 0) {
                this.showNotification('No active downloads to cancel', 'info');
                return;
            }

            // Show confirmation dialog
            const confirmed = confirm(`Are you sure you want to cancel ${activeDownloads.length} active download(s)?`);
            if (!confirmed) {
                return;
            }

            // Cancel all active downloads
            let cancelledCount = 0;
            const cancelPromises = activeDownloads.map(async (id) => {
                try {
                    await window.electronAPI.cancelDownload(id);
                    cancelledCount++;
                } catch (error) {
                    console.error(`Failed to cancel download ${id}:`, error);
                }
            });

            Promise.all(cancelPromises).then(() => {
                if (cancelledCount > 0) {
                    this.showNotification(`Cancelled ${cancelledCount} download(s)`, 'success');
                } else {
                    this.showNotification('Failed to cancel downloads', 'error');
                }
            });

        } catch (error) {
            console.error('Error cancelling all downloads:', error);
            this.showNotification('Failed to cancel downloads', 'error');
        }
    }

    switchToDownloadsTab() {
        // If there's a downloads tab or section, switch to it
        const downloadsTab = document.querySelector('[data-tab="downloads"]');
        const downloadsSection = document.querySelector('.downloads-section');

        if (downloadsTab) {
            downloadsTab.click();
        } else if (downloadsSection) {
            downloadsSection.scrollIntoView({ behavior: 'smooth' });
        }
    }

    // Utility method to get download statistics
    getDownloadStats() {
        if (!window.downloadManager) {
            return {
                total: 0,
                downloading: 0,
                completed: 0,
                paused: 0,
                error: 0,
                cancelled: 0
            };
        }

        const stats = {
            total: window.downloadManager.downloads.size,
            downloading: 0,
            completed: 0,
            paused: 0,
            error: 0,
            cancelled: 0,
            pending: 0
        };

        window.downloadManager.downloads.forEach(download => {
            if (stats.hasOwnProperty(download.status)) {
                stats[download.status]++;
            }
        });

        return stats;
    }

    generateSmartSuggestions(videoInfo) {
        try {
            const suggestions = this.calculateOptimalSettings(videoInfo);
            this.displaySuggestions(suggestions);
        } catch (error) {
            console.error('Error in generateSmartSuggestions:', error);
            // Fail silently to not break the main download functionality
        }
    }

    calculateOptimalSettings(videoInfo) {
        const suggestions = {
            quality: 'best',
            format: 'auto',
            type: 'video',
            reasoning: [],
            confidence: 0
        };

        // Ensure reasoning is always an array
        if (!Array.isArray(suggestions.reasoning)) {
            suggestions.reasoning = [];
        }

        // Analyze video duration
        if (videoInfo.duration) {
            const duration = this.parseDuration(videoInfo.duration);
            if (duration > 3600) { // > 1 hour
                suggestions.quality = '720p';
                suggestions.reasoning.push('Long video detected - 720p recommended for smaller file size');
                suggestions.confidence += 20;
            } else if (duration < 300) { // < 5 minutes
                suggestions.quality = 'best';
                suggestions.reasoning.push('Short video - best quality recommended');
                suggestions.confidence += 15;
            }
        }

        // Analyze available formats and sizes
        if (videoInfo.formats) {
            const formats = videoInfo.formats;
            const has4K = formats.some(f => f.height >= 2160);
            const hasHD = formats.some(f => f.height >= 1080);

            if (has4K && this.isHighEndDevice()) {
                suggestions.quality = '4k';
                suggestions.reasoning.push('4K available and device capable');
                suggestions.confidence += 25;
            } else if (hasHD) {
                suggestions.quality = '1080p';
                suggestions.reasoning.push('HD quality available');
                suggestions.confidence += 20;
            }
        }

        // Check user's previous preferences
        const userPrefs = this.getUserPreferences();
        if (userPrefs.preferredQuality) {
            suggestions.quality = userPrefs.preferredQuality;
            suggestions.reasoning.push(`Based on your usual preference: ${userPrefs.preferredQuality}`);
            suggestions.confidence += 30;
        }

        // Analyze platform-specific recommendations
        const platform = this.detectPlatform(videoInfo.webpage_url || videoInfo.url);
        const platformSuggestions = this.getPlatformSuggestions(platform);
        if (platformSuggestions) {
            // Only copy specific properties, not the whole object
            if (platformSuggestions.quality) suggestions.quality = platformSuggestions.quality;
            if (platformSuggestions.format) suggestions.format = platformSuggestions.format;
            if (platformSuggestions.type) suggestions.type = platformSuggestions.type;

            suggestions.reasoning.push(`Optimized for ${platform}`);
            suggestions.confidence += 15;
        }

        // Network speed consideration
        if (navigator.connection) {
            const connection = navigator.connection;
            if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                suggestions.quality = '360p';
                suggestions.reasoning.push('Slow network detected - lower quality recommended');
                suggestions.confidence += 20;
            } else if (connection.effectiveType === '4g') {
                suggestions.quality = 'best';
                suggestions.reasoning.push('Fast network detected - best quality available');
                suggestions.confidence += 15;
            }
        }

        return suggestions;
    }

    displaySuggestions(suggestions) {
        // Create suggestions panel if it doesn't exist
        let suggestionsPanel = document.getElementById('smart-suggestions');
        if (!suggestionsPanel) {
            suggestionsPanel = this.createSuggestionsPanel();
        }

        const confidence = Math.min(suggestions.confidence, 100);
        const confidenceClass = confidence > 70 ? 'high' : confidence > 40 ? 'medium' : 'low';

        suggestionsPanel.innerHTML = `
            <div class="suggestions-header">
                <h3>🎯 Smart Suggestions</h3>
                <div class="confidence-indicator ${confidenceClass}">
                    ${confidence}% confidence
                </div>
            </div>
            <div class="suggestions-content">
                <div class="suggested-settings">
                    <div class="setting-item">
                        <span class="setting-label">Quality:</span>
                        <span class="setting-value">${suggestions.quality}</span>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">Format:</span>
                        <span class="setting-value">${suggestions.format}</span>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">Type:</span>
                        <span class="setting-value">${suggestions.type}</span>
                    </div>
                </div>
                <div class="reasoning">
                    <h4>Why these settings?</h4>
                    <ul>
                        ${suggestions.reasoning.map(reason => `<li>${reason}</li>`).join('')}
                    </ul>
                </div>
                <div class="suggestion-actions">
                    <button class="btn-primary apply-suggestions">
                        Apply Suggestions
                    </button>
                    <button class="btn-secondary dismiss-suggestions">
                        Dismiss
                    </button>
                </div>
            </div>
        `;

        // Add event listeners
        const applyBtn = suggestionsPanel.querySelector('.apply-suggestions');
        const dismissBtn = suggestionsPanel.querySelector('.dismiss-suggestions');

        applyBtn.addEventListener('click', () => this.applySuggestedSettings(suggestions));
        dismissBtn.addEventListener('click', () => this.dismissSuggestions());

        suggestionsPanel.classList.remove('hidden');
    }

    createSuggestionsPanel() {
        const panel = document.createElement('div');
        panel.id = 'smart-suggestions';
        panel.className = 'smart-suggestions-panel hidden';

        // Insert after download options
        const downloadOptions = document.getElementById('download-options');
        downloadOptions.parentNode.insertBefore(panel, downloadOptions.nextSibling);

        return panel;
    }

    applySuggestedSettings(suggestions) {
        // Apply the suggested settings to the form
        const qualitySelect = document.getElementById('quality-select');
        const formatSelect = document.getElementById('format-select');
        const typeSelect = document.getElementById('download-type');

        if (qualitySelect) qualitySelect.value = suggestions.quality;
        if (formatSelect) formatSelect.value = suggestions.format;
        if (typeSelect) typeSelect.value = suggestions.type;

        // Save as user preference
        this.saveUserPreference('preferredQuality', suggestions.quality);

        this.showNotification('Smart suggestions applied!', 'success');
        this.dismissSuggestions();
    }

    dismissSuggestions() {
        const panel = document.getElementById('smart-suggestions');
        if (panel) {
            panel.classList.add('hidden');
        }
    }

    // Helper methods for smart suggestions
    parseDuration(duration) {
        if (typeof duration === 'number') return duration;
        if (typeof duration === 'string') {
            const parts = duration.split(':').map(Number);
            if (parts.length === 3) {
                return parts[0] * 3600 + parts[1] * 60 + parts[2];
            } else if (parts.length === 2) {
                return parts[0] * 60 + parts[1];
            }
        }
        return 0;
    }

    isHighEndDevice() {
        // Simple heuristic based on available features
        return navigator.hardwareConcurrency >= 8 &&
               navigator.deviceMemory >= 8 &&
               window.screen.width >= 1920;
    }

    getUserPreferences() {
        return {
            preferredQuality: localStorage.getItem('preferredQuality') || null,
            preferredFormat: localStorage.getItem('preferredFormat') || null
        };
    }

    saveUserPreference(key, value) {
        localStorage.setItem(key, value);
    }

    detectPlatform(url) {
        if (!url) return 'Unknown';
        if (url.includes('youtube.com') || url.includes('youtu.be')) return 'YouTube';
        if (url.includes('facebook.com') || url.includes('fb.watch')) return 'Facebook';
        if (url.includes('instagram.com')) return 'Instagram';
        if (url.includes('tiktok.com')) return 'TikTok';
        if (url.includes('twitter.com') || url.includes('x.com')) return 'Twitter';
        if (url.includes('pinterest.com') || url.includes('pin.it')) return 'Pinterest';
        return 'Unknown';
    }

    getPlatformSuggestions(platform) {
        const platformSettings = {
            'YouTube': { format: 'mp4' },
            'TikTok': { quality: '720p', format: 'mp4' },
            'Instagram': { quality: '720p', format: 'mp4' },
            'Twitter': { quality: '720p', format: 'mp4' },
            'Pinterest': { quality: '720p', format: 'mp4' }
        };

        return platformSettings[platform] || null;
    }

    setupQuickActions() {
        // Create quick actions menu
        this.createQuickActionsMenu();

        // Add right-click context menu to download button and URL input
        const downloadButton = document.getElementById('download-button');
        const urlInput = document.getElementById('url-input');

        [downloadButton, urlInput].forEach(element => {
            element.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                if (this.currentUrl && this.isValidUrl(this.currentUrl)) {
                    this.showQuickActionsMenu(e.clientX, e.clientY);
                }
            });
        });

        // Hide menu when clicking elsewhere
        document.addEventListener('click', () => {
            this.hideQuickActionsMenu();
        });

        // Add quick action button next to download button
        this.addQuickActionButton();
    }

    createQuickActionsMenu() {
        const menu = document.createElement('div');
        menu.id = 'quick-actions-menu';
        menu.className = 'quick-actions-menu hidden';

        menu.innerHTML = `
            <div class="quick-actions-header">
                <span class="icon">⚡</span>
                <span>Quick Actions</span>
            </div>
            <div class="quick-actions-list">
                <button class="quick-action-item" data-action="audio-only">
                    <span class="icon">🎵</span>
                    <span class="label">Download Audio Only</span>
                    <span class="shortcut">Ctrl+A</span>
                </button>
                <button class="quick-action-item" data-action="best-quality">
                    <span class="icon">🎬</span>
                    <span class="label">Download Best Quality</span>
                    <span class="shortcut">Ctrl+B</span>
                </button>
                <button class="quick-action-item" data-action="720p">
                    <span class="icon">📺</span>
                    <span class="label">Download 720p</span>
                    <span class="shortcut">Ctrl+7</span>
                </button>
                <button class="quick-action-item" data-action="480p">
                    <span class="icon">📱</span>
                    <span class="label">Download 480p</span>
                    <span class="shortcut">Ctrl+4</span>
                </button>
                <div class="quick-actions-separator"></div>
                <button class="quick-action-item" data-action="custom-folder">
                    <span class="icon">📁</span>
                    <span class="label">Choose Download Folder</span>
                    <span class="shortcut">Ctrl+F</span>
                </button>
                <button class="quick-action-item" data-action="add-to-queue">
                    <span class="icon">📋</span>
                    <span class="label">Add to Queue</span>
                    <span class="shortcut">Ctrl+Q</span>
                </button>
                <div class="quick-actions-separator"></div>
                <button class="quick-action-item" data-action="copy-info">
                    <span class="icon">📄</span>
                    <span class="label">Copy Video Info</span>
                    <span class="shortcut">Ctrl+I</span>
                </button>
                <button class="quick-action-item" data-action="show-shortcuts">
                    <span class="icon">⌨️</span>
                    <span class="label">Keyboard Shortcuts</span>
                    <span class="shortcut">F1</span>
                </button>
            </div>
        `;

        // Add event listeners to quick action items
        menu.addEventListener('click', (e) => {
            e.stopPropagation();
            const actionItem = e.target.closest('.quick-action-item');
            if (actionItem) {
                const action = actionItem.dataset.action;
                this.executeQuickAction(action);
                this.hideQuickActionsMenu();
            }
        });

        document.body.appendChild(menu);
    }

    addQuickActionButton() {
        const downloadButton = document.getElementById('download-button');
        if (!downloadButton) {
            console.error('Download button not found, cannot add quick action button');
            return;
        }

        // Check if button already exists
        if (document.getElementById('quick-action-button')) {
            console.log('Quick action button already exists');
            return;
        }

        const quickActionBtn = document.createElement('button');
        quickActionBtn.id = 'quick-action-button';
        quickActionBtn.className = 'quick-action-button';
        quickActionBtn.innerHTML = '<span class="icon">⚡</span>';
        quickActionBtn.title = 'Quick Actions (Right-click for menu)';

        quickActionBtn.addEventListener('click', (e) => {
            e.preventDefault();
            console.log('Quick action button clicked');
            console.log('Current URL:', this.currentUrl);
            console.log('Is valid URL:', this.currentUrl ? this.isValidUrl(this.currentUrl) : false);

            // Show menu even if no URL is entered, but with limited options
            const rect = quickActionBtn.getBoundingClientRect();
            this.showQuickActionsMenu(rect.left, rect.bottom + 5);
        });

        // Add right-click support
        quickActionBtn.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            const rect = quickActionBtn.getBoundingClientRect();
            this.showQuickActionsMenu(rect.left, rect.bottom + 5);
        });

        // Insert after download button
        downloadButton.parentNode.insertBefore(quickActionBtn, downloadButton.nextSibling);
        console.log('Quick action button added successfully');
    }

    showQuickActionsMenu(x, y) {
        const menu = document.getElementById('quick-actions-menu');
        menu.style.left = `${x}px`;
        menu.style.top = `${y}px`;
        menu.classList.remove('hidden');

        // Adjust position if menu goes off screen
        const rect = menu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            menu.style.left = `${x - rect.width}px`;
        }
        if (rect.bottom > window.innerHeight) {
            menu.style.top = `${y - rect.height}px`;
        }
    }

    hideQuickActionsMenu() {
        const menu = document.getElementById('quick-actions-menu');
        if (menu) {
            menu.classList.add('hidden');
        }
    }

    showHelpModal() {
        // For now, just show the about modal or a simple alert
        const aboutModal = document.getElementById('about-modal');
        if (aboutModal) {
            aboutModal.classList.remove('hidden');
        } else {
            this.showNotification('Help: Use F1 for keyboard shortcuts, right-click for context menus', 'info');
        }
    }

    async executeQuickAction(action) {
        console.log('Executing quick action:', action);

        // Actions that don't require a URL
        const noUrlActions = ['custom-folder', 'show-shortcuts', 'show-help'];

        if (!noUrlActions.includes(action) && (!this.currentUrl || !this.isValidUrl(this.currentUrl))) {
            this.showNotification('Please enter a valid URL first', 'warning');
            return;
        }

        try {
            switch (action) {
                case 'audio-only':
                    await this.quickDownload({ type: 'audio', quality: 'best', format: 'mp3' });
                    break;
                case 'best-quality':
                    await this.quickDownload({ type: 'video', quality: 'best', format: 'auto' });
                    break;
                case '720p':
                    await this.quickDownload({ type: 'video', quality: '720p', format: 'mp4' });
                    break;
                case '480p':
                    await this.quickDownload({ type: 'video', quality: '480p', format: 'mp4' });
                    break;
                case 'custom-folder':
                    await this.chooseCustomFolder();
                    break;
                case 'add-to-queue':
                    await this.addToDownloadQueue();
                    break;
                case 'copy-info':
                    await this.copyVideoInfo();
                    break;
                case 'show-shortcuts':
                    this.showKeyboardShortcuts();
                    break;
                case 'show-help':
                    this.showHelpModal();
                    break;
                default:
                    console.warn('Unknown quick action:', action);
                    this.showNotification(`Unknown action: ${action}`, 'error');
            }
        } catch (error) {
            console.error('Error executing quick action:', error);
            this.showNotification(`Failed to execute action: ${action}`, 'error');
        }
    }

    async quickDownload(options) {
        try {
            // Set the options in the form
            if (options.type) {
                const typeSelect = document.getElementById('download-type');
                if (typeSelect) typeSelect.value = options.type;
            }
            if (options.quality) {
                const qualitySelect = document.getElementById('quality-select');
                if (qualitySelect) qualitySelect.value = options.quality;
            }
            if (options.format) {
                const formatSelect = document.getElementById('format-select');
                if (formatSelect) formatSelect.value = options.format;
            }

            // Start the download
            await this.startDownload();
            this.showNotification(`Quick download started: ${options.quality} ${options.type}`, 'success');
        } catch (error) {
            this.showNotification('Failed to start quick download', 'error');
            console.error('Quick download error:', error);
        }
    }

    async chooseCustomFolder() {
        try {
            const folder = await window.electronAPI.selectFolder();
            if (folder) {
                const destinationSelect = document.getElementById('destination-select');
                // Add custom option if it doesn't exist
                let customOption = destinationSelect.querySelector('option[value="custom"]');
                if (!customOption) {
                    customOption = document.createElement('option');
                    customOption.value = 'custom';
                    customOption.textContent = 'Custom Folder';
                    destinationSelect.appendChild(customOption);
                }
                destinationSelect.value = 'custom';
                this.customDestination = folder;
                this.showNotification(`Download folder set to: ${folder}`, 'success');
            }
        } catch (error) {
            this.showNotification('Failed to select folder', 'error');
        }
    }

    async addToDownloadQueue() {
        // Add current URL to a download queue for batch processing later
        if (!this.downloadQueue) {
            this.downloadQueue = [];
        }

        const queueItem = {
            url: this.currentUrl,
            title: this.videoInfo?.title || 'Unknown',
            addedAt: new Date().toISOString()
        };

        this.downloadQueue.push(queueItem);
        this.showNotification(`Added to download queue (${this.downloadQueue.length} items)`, 'success');

        // Update queue display if it exists
        this.updateQueueDisplay();
    }

    async copyVideoInfo() {
        if (!this.videoInfo) {
            this.showNotification('No video information available', 'warning');
            return;
        }

        const info = {
            title: this.videoInfo.title,
            url: this.currentUrl,
            duration: this.videoInfo.duration,
            uploader: this.videoInfo.uploader,
            view_count: this.videoInfo.view_count,
            upload_date: this.videoInfo.upload_date
        };

        try {
            await window.clipboardAPI.writeText(JSON.stringify(info, null, 2));
            this.showNotification('Video information copied to clipboard', 'success');
        } catch (error) {
            console.error('Copy to clipboard error:', error);
            this.showNotification('Failed to copy video information', 'error');
        }
    }

    updateQueueDisplay() {
        // This would update a queue display panel if implemented
        // For now, just log the queue
        console.log('Download queue updated:', this.downloadQueue);
    }

    setupKeyboardShortcuts() {
        // Global keyboard shortcut handler
        document.addEventListener('keydown', (e) => {
            // Don't trigger shortcuts when typing in input fields
            if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                // Allow some shortcuts even in input fields
                if (e.ctrlKey || e.metaKey) {
                    this.handleInputFieldShortcuts(e);
                }
                return;
            }

            this.handleGlobalShortcuts(e);
        });

        // Show keyboard shortcuts help
        this.createShortcutsHelp();
    }

    handleInputFieldShortcuts(e) {
        const key = e.key.toLowerCase();

        // Ctrl/Cmd + Enter to start download from URL input
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            if (this.currentUrl && this.isValidUrl(this.currentUrl)) {
                this.startDownload();
            }
        }

        // Ctrl/Cmd + V to paste and auto-process
        if ((e.ctrlKey || e.metaKey) && key === 'v') {
            setTimeout(() => {
                const urlInput = document.getElementById('url-input');
                if (urlInput && urlInput === e.target) {
                    this.handleUrlInput(urlInput.value);
                }
            }, 10);
        }
    }

    handleGlobalShortcuts(e) {
        const key = e.key.toLowerCase();
        const isCtrl = e.ctrlKey || e.metaKey;
        const isShift = e.shiftKey;
        const isAlt = e.altKey;

        // Prevent default for our shortcuts
        if (this.isOurShortcut(e)) {
            e.preventDefault();
        }

        // Quick download shortcuts
        if (isCtrl && this.currentUrl && this.isValidUrl(this.currentUrl)) {
            switch (key) {
                case 'a':
                    this.executeQuickAction('audio-only');
                    break;
                case 'b':
                    this.executeQuickAction('best-quality');
                    break;
                case '7':
                    this.executeQuickAction('720p');
                    break;
                case '4':
                    this.executeQuickAction('480p');
                    break;
                case 'f':
                    this.executeQuickAction('custom-folder');
                    break;
                case 'q':
                    this.executeQuickAction('add-to-queue');
                    break;
                case 'i':
                    this.executeQuickAction('copy-info');
                    break;
            }
        }

        // Navigation shortcuts
        if (isCtrl) {
            switch (key) {
                case ',':
                    this.openSettings();
                    break;
                case 'h':
                    this.showDownloadHistory();
                    break;
                case 'n':
                    this.focusUrlInput();
                    break;
                case 'r':
                    this.refreshCurrentVideo();
                    break;
                case 's':
                    this.openPlatformSearch('youtube');
                    break;
            }
        }

        // Theme and UI shortcuts
        if (isCtrl && isShift) {
            switch (key) {
                case 't':
                    this.toggleTheme();
                    break;
                case 'd':
                    this.toggleDarkMode();
                    break;
                case 'c':
                    this.clearAllDownloads();
                    break;
            }
        }

        // Function keys
        switch (e.key) {
            case 'F1':
                e.preventDefault();
                this.showKeyboardShortcuts();
                break;
            case 'F5':
                e.preventDefault();
                this.refreshApp();
                break;
            case 'Escape':
                this.handleEscapeKey();
                break;
            case 'Enter':
                if (this.currentUrl && this.isValidUrl(this.currentUrl)) {
                    this.startDownload();
                }
                break;
        }

        // Number keys for quality selection (when download options are visible)
        if (!isCtrl && !isShift && !isAlt && /^[1-9]$/.test(key)) {
            this.selectQualityByNumber(parseInt(key));
        }

        // Arrow keys for navigation
        if (e.key.startsWith('Arrow')) {
            this.handleArrowKeys(e);
        }
    }

    isOurShortcut(e) {
        const key = e.key.toLowerCase();
        const isCtrl = e.ctrlKey || e.metaKey;
        const isShift = e.shiftKey;

        // Quick action shortcuts
        if (isCtrl && ['a', 'b', '7', '4', 'f', 'q', 'i'].includes(key)) return true;

        // Navigation shortcuts
        if (isCtrl && [',', 'h', 'n', 'r', 's'].includes(key)) return true;

        // Theme shortcuts
        if (isCtrl && isShift && ['t', 'd', 'c'].includes(key)) return true;

        // Function keys
        if (['F1', 'F5'].includes(e.key)) return true;

        return false;
    }

    focusUrlInput() {
        const urlInput = document.getElementById('url-input');
        urlInput.focus();
        urlInput.select();
    }

    refreshCurrentVideo() {
        if (this.currentUrl && this.isValidUrl(this.currentUrl)) {
            this.loadVideoInfo(this.currentUrl);
            this.showNotification('Video information refreshed', 'info');
        }
    }

    toggleDarkMode() {
        // Force dark mode toggle regardless of system preference
        const currentTheme = document.body.classList.contains('theme-dark') ? 'light' : 'dark';
        this.applyTheme(currentTheme);
    }

    clearAllDownloads() {
        if (window.downloadManager) {
            window.downloadManager.clearAll();
            this.showNotification('All downloads cleared', 'info');
        }
    }

    refreshApp() {
        window.location.reload();
    }

    handleEscapeKey() {
        // Close any open modals
        const openModal = document.querySelector('.modal:not(.hidden)');
        if (openModal) {
            this.closeModal(openModal.id);
            return;
        }

        // Hide quick actions menu
        this.hideQuickActionsMenu();

        // Clear URL input if focused
        const urlInput = document.getElementById('url-input');
        if (document.activeElement === urlInput) {
            urlInput.blur();
        }
    }

    selectQualityByNumber(number) {
        const qualitySelect = document.getElementById('quality-select');
        if (!qualitySelect || qualitySelect.style.display === 'none') return;

        const options = qualitySelect.options;
        if (number <= options.length) {
            qualitySelect.selectedIndex = number - 1;
            this.showNotification(`Quality set to: ${options[number - 1].text}`, 'info');
        }
    }

    handleArrowKeys(e) {
        // Navigate through download items
        const downloadItems = document.querySelectorAll('.download-item');
        if (downloadItems.length === 0) return;

        let currentIndex = -1;
        const focused = document.querySelector('.download-item.focused');
        if (focused) {
            currentIndex = Array.from(downloadItems).indexOf(focused);
        }

        switch (e.key) {
            case 'ArrowDown':
                e.preventDefault();
                currentIndex = Math.min(currentIndex + 1, downloadItems.length - 1);
                break;
            case 'ArrowUp':
                e.preventDefault();
                currentIndex = Math.max(currentIndex - 1, 0);
                break;
        }

        // Remove previous focus and add to new item
        downloadItems.forEach(item => item.classList.remove('focused'));
        if (currentIndex >= 0) {
            downloadItems[currentIndex].classList.add('focused');
            downloadItems[currentIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    createShortcutsHelp() {
        // Create keyboard shortcuts help modal
        const helpModal = document.createElement('div');
        helpModal.id = 'shortcuts-help-modal';
        helpModal.className = 'modal hidden';

        helpModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>⌨️ Keyboard Shortcuts</h2>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="shortcuts-grid">
                        <div class="shortcuts-section">
                            <h3>Quick Downloads</h3>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>A</kbd>
                                <span>Download Audio Only</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>B</kbd>
                                <span>Download Best Quality</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>7</kbd>
                                <span>Download 720p</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>4</kbd>
                                <span>Download 480p</span>
                            </div>
                        </div>

                        <div class="shortcuts-section">
                            <h3>Navigation</h3>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>N</kbd>
                                <span>Focus URL Input</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>H</kbd>
                                <span>Show History</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>,</kbd>
                                <span>Open Settings</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>S</kbd>
                                <span>Search YouTube</span>
                            </div>
                        </div>

                        <div class="shortcuts-section">
                            <h3>Actions</h3>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>F</kbd>
                                <span>Choose Folder</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>Q</kbd>
                                <span>Add to Queue</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>I</kbd>
                                <span>Copy Video Info</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>R</kbd>
                                <span>Refresh Video</span>
                            </div>
                        </div>

                        <div class="shortcuts-section">
                            <h3>Interface</h3>
                            <div class="shortcut-item">
                                <kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>T</kbd>
                                <span>Toggle Theme</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>F1</kbd>
                                <span>Show This Help</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>F5</kbd>
                                <span>Refresh App</span>
                            </div>
                            <div class="shortcut-item">
                                <kbd>Esc</kbd>
                                <span>Close Modals</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(helpModal);
        this.setupModalHandlers();
    }

    showKeyboardShortcuts() {
        const modal = document.getElementById('shortcuts-help-modal');
        modal.classList.remove('hidden');
    }

    // Download Scheduler Implementation
    setupDownloadScheduler() {
        this.scheduledDownloads = new Map();
        this.schedulerInterval = null;

        // Start scheduler checker
        this.startSchedulerChecker();

        // Create scheduler modal
        this.createSchedulerModal();
    }

    createSchedulerModal() {
        const schedulerModal = document.createElement('div');
        schedulerModal.id = 'scheduler-modal';
        schedulerModal.className = 'modal hidden';

        schedulerModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>⏰ Schedule Download</h2>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="scheduler-form">
                        <div class="form-group">
                            <label for="schedule-type">Schedule Type:</label>
                            <select id="schedule-type" class="form-control">
                                <option value="specific">Specific Date & Time</option>
                                <option value="delay">Delay (Minutes)</option>
                                <option value="recurring">Recurring</option>
                            </select>
                        </div>

                        <div id="specific-time-group" class="form-group">
                            <label for="schedule-date">Date:</label>
                            <input type="date" id="schedule-date" class="form-control">
                            <label for="schedule-time">Time:</label>
                            <input type="time" id="schedule-time" class="form-control">
                        </div>

                        <div id="delay-group" class="form-group hidden">
                            <label for="delay-minutes">Delay (minutes):</label>
                            <input type="number" id="delay-minutes" class="form-control" min="1" max="1440" value="30">
                        </div>

                        <div id="recurring-group" class="form-group hidden">
                            <label for="recurring-pattern">Pattern:</label>
                            <select id="recurring-pattern" class="form-control">
                                <option value="daily">Daily</option>
                                <option value="weekly">Weekly</option>
                                <option value="monthly">Monthly</option>
                            </select>
                            <label for="recurring-time">Time:</label>
                            <input type="time" id="recurring-time" class="form-control">
                        </div>

                        <div class="form-group">
                            <label for="schedule-priority">Priority:</label>
                            <select id="schedule-priority" class="form-control">
                                <option value="low">Low</option>
                                <option value="normal" selected>Normal</option>
                                <option value="high">High</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="schedule-notes">Notes (optional):</label>
                            <textarea id="schedule-notes" class="form-control" rows="3" placeholder="Add notes about this scheduled download..."></textarea>
                        </div>

                        <div class="scheduled-downloads-list">
                            <h3>Scheduled Downloads</h3>
                            <div id="scheduled-items"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="window.app.closeSchedulerModal()">Cancel</button>
                    <button class="btn-primary" onclick="window.app.scheduleDownload()">Schedule Download</button>
                </div>
            </div>
        `;

        document.body.appendChild(schedulerModal);

        // Setup form interactions
        this.setupSchedulerFormHandlers();
        this.setupModalHandlers();
    }

    setupSchedulerFormHandlers() {
        const scheduleType = document.getElementById('schedule-type');
        const specificGroup = document.getElementById('specific-time-group');
        const delayGroup = document.getElementById('delay-group');
        const recurringGroup = document.getElementById('recurring-group');

        scheduleType.addEventListener('change', (e) => {
            // Hide all groups
            specificGroup.classList.add('hidden');
            delayGroup.classList.add('hidden');
            recurringGroup.classList.add('hidden');

            // Show relevant group
            switch (e.target.value) {
                case 'specific':
                    specificGroup.classList.remove('hidden');
                    break;
                case 'delay':
                    delayGroup.classList.remove('hidden');
                    break;
                case 'recurring':
                    recurringGroup.classList.remove('hidden');
                    break;
            }
        });

        // Set default date to today
        const today = new Date();
        document.getElementById('schedule-date').value = today.toISOString().split('T')[0];

        // Set default time to 1 hour from now
        const oneHourLater = new Date(today.getTime() + 60 * 60 * 1000);
        document.getElementById('schedule-time').value = oneHourLater.toTimeString().slice(0, 5);
    }

    showSchedulerModal() {
        if (!this.currentUrl || !this.isValidUrl(this.currentUrl)) {
            this.showNotification('Please enter a valid video URL first', 'warning');
            return;
        }

        this.updateScheduledDownloadsList();
        const modal = document.getElementById('scheduler-modal');
        modal.classList.remove('hidden');
    }

    closeSchedulerModal() {
        const modal = document.getElementById('scheduler-modal');
        modal.classList.add('hidden');
    }

    scheduleDownload() {
        const scheduleType = document.getElementById('schedule-type').value;
        const priority = document.getElementById('schedule-priority').value;
        const notes = document.getElementById('schedule-notes').value;

        let scheduledTime;
        let recurring = false;
        let recurringPattern = null;

        try {
            switch (scheduleType) {
                case 'specific':
                    const date = document.getElementById('schedule-date').value;
                    const time = document.getElementById('schedule-time').value;
                    if (!date || !time) {
                        throw new Error('Please select both date and time');
                    }
                    scheduledTime = new Date(`${date}T${time}`);
                    break;

                case 'delay':
                    const delayMinutes = parseInt(document.getElementById('delay-minutes').value);
                    if (!delayMinutes || delayMinutes < 1) {
                        throw new Error('Please enter a valid delay in minutes');
                    }
                    scheduledTime = new Date(Date.now() + delayMinutes * 60 * 1000);
                    break;

                case 'recurring':
                    const recurringTime = document.getElementById('recurring-time').value;
                    recurringPattern = document.getElementById('recurring-pattern').value;
                    if (!recurringTime) {
                        throw new Error('Please select a time for recurring download');
                    }

                    // Calculate next occurrence
                    scheduledTime = this.calculateNextRecurringTime(recurringTime, recurringPattern);
                    recurring = true;
                    break;
            }

            // Validate scheduled time is in the future
            if (scheduledTime <= new Date()) {
                throw new Error('Scheduled time must be in the future');
            }

            // Create scheduled download object
            const scheduledDownload = {
                id: Date.now().toString(),
                url: this.currentUrl,
                videoInfo: this.videoInfo,
                scheduledTime: scheduledTime,
                priority: priority,
                notes: notes,
                recurring: recurring,
                recurringPattern: recurringPattern,
                status: 'scheduled',
                downloadSettings: this.getCurrentDownloadSettings()
            };

            // Add to scheduled downloads
            this.scheduledDownloads.set(scheduledDownload.id, scheduledDownload);

            // Save to storage
            this.saveScheduledDownloads();

            // Update UI
            this.updateScheduledDownloadsList();

            // Show success message
            const timeStr = scheduledTime.toLocaleString();
            this.showNotification(`Download scheduled for ${timeStr}`, 'success');

            // Clear form
            this.clearSchedulerForm();

        } catch (error) {
            this.showNotification(error.message, 'error');
        }
    }

    calculateNextRecurringTime(time, pattern) {
        const now = new Date();
        const [hours, minutes] = time.split(':').map(Number);

        let nextTime = new Date();
        nextTime.setHours(hours, minutes, 0, 0);

        // If time has passed today, move to next occurrence
        if (nextTime <= now) {
            switch (pattern) {
                case 'daily':
                    nextTime.setDate(nextTime.getDate() + 1);
                    break;
                case 'weekly':
                    nextTime.setDate(nextTime.getDate() + 7);
                    break;
                case 'monthly':
                    nextTime.setMonth(nextTime.getMonth() + 1);
                    break;
            }
        }

        return nextTime;
    }

    getCurrentDownloadSettings() {
        return {
            quality: document.getElementById('quality-select')?.value || 'best',
            format: document.getElementById('format-select')?.value || 'auto',
            destination: this.settings.defaultDestination || 'downloads'
        };
    }

    clearSchedulerForm() {
        document.getElementById('schedule-notes').value = '';
        document.getElementById('delay-minutes').value = '30';
        document.getElementById('schedule-priority').value = 'normal';

        // Reset to default date/time
        const today = new Date();
        document.getElementById('schedule-date').value = today.toISOString().split('T')[0];
        const oneHourLater = new Date(today.getTime() + 60 * 60 * 1000);
        document.getElementById('schedule-time').value = oneHourLater.toTimeString().slice(0, 5);
    }

    updateScheduledDownloadsList() {
        const container = document.getElementById('scheduled-items');
        if (!container) return;

        container.innerHTML = '';

        if (this.scheduledDownloads.size === 0) {
            container.innerHTML = '<p class="no-scheduled">No scheduled downloads</p>';
            return;
        }

        // Sort by scheduled time
        const sortedDownloads = Array.from(this.scheduledDownloads.values())
            .sort((a, b) => a.scheduledTime - b.scheduledTime);

        sortedDownloads.forEach(download => {
            const item = document.createElement('div');
            item.className = 'scheduled-item';

            const timeStr = download.scheduledTime.toLocaleString();
            const title = download.videoInfo?.title || 'Unknown Video';
            const priorityIcon = download.priority === 'high' ? '🔴' : download.priority === 'low' ? '🟡' : '🟢';

            item.innerHTML = `
                <div class="scheduled-item-header">
                    <span class="priority-icon">${priorityIcon}</span>
                    <span class="scheduled-title">${title}</span>
                    <button class="remove-scheduled" onclick="window.app.removeScheduledDownload('${download.id}')" title="Remove">×</button>
                </div>
                <div class="scheduled-item-details">
                    <div class="scheduled-time">⏰ ${timeStr}</div>
                    ${download.recurring ? `<div class="recurring-info">🔄 ${download.recurringPattern}</div>` : ''}
                    ${download.notes ? `<div class="scheduled-notes">${download.notes}</div>` : ''}
                </div>
            `;

            container.appendChild(item);
        });
    }

    removeScheduledDownload(id) {
        this.scheduledDownloads.delete(id);
        this.saveScheduledDownloads();
        this.updateScheduledDownloadsList();
        this.showNotification('Scheduled download removed', 'info');
    }

    startSchedulerChecker() {
        // Check every 30 seconds for scheduled downloads
        this.schedulerInterval = setInterval(() => {
            this.checkScheduledDownloads();
        }, 30000);
    }

    checkScheduledDownloads() {
        const now = new Date();

        for (const [id, download] of this.scheduledDownloads) {
            if (download.status === 'scheduled' && download.scheduledTime <= now) {
                this.executeScheduledDownload(download);
            }
        }
    }

    async executeScheduledDownload(scheduledDownload) {
        try {
            // Mark as executing
            scheduledDownload.status = 'executing';

            // Set up the download
            this.currentUrl = scheduledDownload.url;
            this.videoInfo = scheduledDownload.videoInfo;

            // Apply saved settings
            const settings = scheduledDownload.downloadSettings;
            if (settings.quality && document.getElementById('quality-select')) {
                document.getElementById('quality-select').value = settings.quality;
            }
            if (settings.format && document.getElementById('format-select')) {
                document.getElementById('format-select').value = settings.format;
            }

            // Start the download
            await this.startDownload();

            // Handle recurring downloads
            if (scheduledDownload.recurring) {
                // Schedule next occurrence
                scheduledDownload.scheduledTime = this.calculateNextRecurringTime(
                    scheduledDownload.scheduledTime.toTimeString().slice(0, 5),
                    scheduledDownload.recurringPattern
                );
                scheduledDownload.status = 'scheduled';
            } else {
                // Remove one-time download
                this.scheduledDownloads.delete(scheduledDownload.id);
            }

            // Save changes
            this.saveScheduledDownloads();

            // Show notification
            const title = scheduledDownload.videoInfo?.title || 'Scheduled Download';
            this.showNotification(`Started scheduled download: ${title}`, 'success');

        } catch (error) {
            console.error('Failed to execute scheduled download:', error);
            scheduledDownload.status = 'failed';
            this.showNotification('Failed to start scheduled download', 'error');
        }
    }

    saveScheduledDownloads() {
        try {
            const data = Array.from(this.scheduledDownloads.entries()).map(([id, download]) => [
                id,
                {
                    ...download,
                    scheduledTime: download.scheduledTime.toISOString()
                }
            ]);
            localStorage.setItem('scheduledDownloads', JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save scheduled downloads:', error);
        }
    }

    loadScheduledDownloads() {
        try {
            const data = localStorage.getItem('scheduledDownloads');
            if (data) {
                const parsed = JSON.parse(data);
                this.scheduledDownloads = new Map(
                    parsed.map(([id, download]) => [
                        id,
                        {
                            ...download,
                            scheduledTime: new Date(download.scheduledTime)
                        }
                    ])
                );
            }
        } catch (error) {
            console.error('Failed to load scheduled downloads:', error);
            this.scheduledDownloads = new Map();
        }
    }

    // Download Templates/Presets Implementation
    setupDownloadTemplates() {
        this.downloadTemplates = new Map();
        this.loadDownloadTemplates();
        this.createTemplatesModal();
        this.addTemplateControls();
    }

    createTemplatesModal() {
        const templatesModal = document.createElement('div');
        templatesModal.id = 'templates-modal';
        templatesModal.className = 'modal hidden';

        templatesModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>📋 Download Templates</h2>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="templates-section">
                        <div class="create-template-section">
                            <h3>Create New Template</h3>
                            <div class="template-form">
                                <div class="form-group">
                                    <label for="template-name">Template Name:</label>
                                    <input type="text" id="template-name" class="form-control" placeholder="e.g., High Quality Video, Audio Only, Mobile Friendly">
                                </div>

                                <div class="form-group">
                                    <label for="template-description">Description (optional):</label>
                                    <textarea id="template-description" class="form-control" rows="2" placeholder="Describe when to use this template..."></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="template-quality">Quality:</label>
                                    <select id="template-quality" class="form-control">
                                        <option value="best">Best Available</option>
                                        <option value="worst">Worst Available</option>
                                        <option value="1080p">1080p</option>
                                        <option value="720p">720p</option>
                                        <option value="480p">480p</option>
                                        <option value="360p">360p</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="template-format">Format:</label>
                                    <select id="template-format" class="form-control">
                                        <option value="auto">Auto</option>
                                        <option value="mp4">MP4</option>
                                        <option value="mkv">MKV</option>
                                        <option value="webm">WebM</option>
                                        <option value="mp3">MP3 (Audio Only)</option>
                                        <option value="aac">AAC (Audio Only)</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="template-destination">Destination:</label>
                                    <select id="template-destination" class="form-control">
                                        <option value="downloads">Downloads Folder</option>
                                        <option value="desktop">Desktop</option>
                                        <option value="documents">Documents</option>
                                        <option value="custom">Custom Folder</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="template-subtitles"> Include Subtitles
                                    </label>
                                </div>

                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="template-thumbnail"> Download Thumbnail
                                    </label>
                                </div>

                                <button class="btn-primary" onclick="window.app.saveTemplate()">Save Template</button>
                            </div>
                        </div>

                        <div class="templates-list-section">
                            <h3>Saved Templates</h3>
                            <div id="templates-list"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="window.app.closeTemplatesModal()">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(templatesModal);
        this.setupModalHandlers();
    }

    addTemplateControls() {
        // Add template dropdown to main interface
        const downloadControls = document.querySelector('.input-controls');
        if (downloadControls) {
            const templateControls = document.createElement('div');
            templateControls.className = 'template-controls';
            templateControls.innerHTML = `
                <select id="template-select" class="template-select">
                    <option value="">Select Template...</option>
                </select>
                <button id="manage-templates-btn" class="manage-templates-btn" onclick="window.app.showTemplatesModal()" title="Manage Templates">
                    📋
                </button>
            `;

            downloadControls.appendChild(templateControls);

            // Setup template selection handler
            document.getElementById('template-select').addEventListener('change', (e) => {
                if (e.target.value) {
                    this.applyTemplate(e.target.value);
                }
            });

            this.updateTemplateDropdown();
        }
    }

    showTemplatesModal() {
        this.updateTemplatesList();
        const modal = document.getElementById('templates-modal');
        modal.classList.remove('hidden');
    }

    closeTemplatesModal() {
        const modal = document.getElementById('templates-modal');
        modal.classList.add('hidden');
        this.clearTemplateForm();
    }

    saveTemplate() {
        const name = document.getElementById('template-name').value.trim();
        const description = document.getElementById('template-description').value.trim();
        const quality = document.getElementById('template-quality').value;
        const format = document.getElementById('template-format').value;
        const destination = document.getElementById('template-destination').value;
        const subtitles = document.getElementById('template-subtitles').checked;
        const thumbnail = document.getElementById('template-thumbnail').checked;

        if (!name) {
            this.showNotification('Please enter a template name', 'warning');
            return;
        }

        // Check if template name already exists
        if (this.downloadTemplates.has(name)) {
            if (!confirm('A template with this name already exists. Do you want to overwrite it?')) {
                return;
            }
        }

        const template = {
            id: Date.now().toString(),
            name: name,
            description: description,
            settings: {
                quality: quality,
                format: format,
                destination: destination,
                subtitles: subtitles,
                thumbnail: thumbnail
            },
            createdAt: new Date().toISOString(),
            usageCount: 0
        };

        this.downloadTemplates.set(name, template);
        this.saveDownloadTemplates();
        this.updateTemplatesList();
        this.updateTemplateDropdown();
        this.clearTemplateForm();

        this.showNotification(`Template "${name}" saved successfully`, 'success');
    }

    applyTemplate(templateName) {
        const template = this.downloadTemplates.get(templateName);
        if (!template) return;

        const settings = template.settings;

        // Apply settings to UI controls
        const qualitySelect = document.getElementById('quality-select');
        const formatSelect = document.getElementById('format-select');

        if (qualitySelect && settings.quality) {
            qualitySelect.value = settings.quality;
        }

        if (formatSelect && settings.format) {
            formatSelect.value = settings.format;
        }

        // Update usage count
        template.usageCount++;
        this.saveDownloadTemplates();

        this.showNotification(`Applied template: ${templateName}`, 'success');
    }

    deleteTemplate(templateName) {
        if (confirm(`Are you sure you want to delete the template "${templateName}"?`)) {
            this.downloadTemplates.delete(templateName);
            this.saveDownloadTemplates();
            this.updateTemplatesList();
            this.updateTemplateDropdown();
            this.showNotification(`Template "${templateName}" deleted`, 'info');
        }
    }

    clearTemplateForm() {
        document.getElementById('template-name').value = '';
        document.getElementById('template-description').value = '';
        document.getElementById('template-quality').value = 'best';
        document.getElementById('template-format').value = 'auto';
        document.getElementById('template-destination').value = 'downloads';
        document.getElementById('template-subtitles').checked = false;
        document.getElementById('template-thumbnail').checked = false;
    }

    updateTemplatesList() {
        const container = document.getElementById('templates-list');
        if (!container) return;

        container.innerHTML = '';

        if (this.downloadTemplates.size === 0) {
            container.innerHTML = '<p class="no-templates">No templates saved yet</p>';
            return;
        }

        // Sort templates by usage count (most used first)
        const sortedTemplates = Array.from(this.downloadTemplates.values())
            .sort((a, b) => b.usageCount - a.usageCount);

        sortedTemplates.forEach(template => {
            const item = document.createElement('div');
            item.className = 'template-item';

            const formatLabel = template.settings.format === 'auto' ? 'Auto' : template.settings.format.toUpperCase();
            const qualityLabel = template.settings.quality === 'best' ? 'Best' : template.settings.quality;

            item.innerHTML = `
                <div class="template-item-header">
                    <span class="template-name">${template.name}</span>
                    <div class="template-actions">
                        <button class="apply-template-btn" onclick="window.app.applyTemplate('${template.name}')" title="Apply Template">
                            ✓
                        </button>
                        <button class="delete-template-btn" onclick="window.app.deleteTemplate('${template.name}')" title="Delete Template">
                            ×
                        </button>
                    </div>
                </div>
                <div class="template-details">
                    <div class="template-settings">
                        <span class="setting-tag">${qualityLabel}</span>
                        <span class="setting-tag">${formatLabel}</span>
                        ${template.settings.subtitles ? '<span class="setting-tag">Subtitles</span>' : ''}
                        ${template.settings.thumbnail ? '<span class="setting-tag">Thumbnail</span>' : ''}
                    </div>
                    ${template.description ? `<div class="template-description">${template.description}</div>` : ''}
                    <div class="template-meta">
                        Used ${template.usageCount} times • Created ${new Date(template.createdAt).toLocaleDateString()}
                    </div>
                </div>
            `;

            container.appendChild(item);
        });
    }

    updateTemplateDropdown() {
        const select = document.getElementById('template-select');
        if (!select) return;

        // Clear existing options except the first one
        select.innerHTML = '<option value="">Select Template...</option>';

        // Add template options
        Array.from(this.downloadTemplates.values())
            .sort((a, b) => a.name.localeCompare(b.name))
            .forEach(template => {
                const option = document.createElement('option');
                option.value = template.name;
                option.textContent = template.name;
                select.appendChild(option);
            });
    }

    saveDownloadTemplates() {
        try {
            const data = Array.from(this.downloadTemplates.entries());
            localStorage.setItem('downloadTemplates', JSON.stringify(data));
        } catch (error) {
            console.error('Failed to save download templates:', error);
        }
    }

    loadDownloadTemplates() {
        try {
            const data = localStorage.getItem('downloadTemplates');
            if (data) {
                const parsed = JSON.parse(data);
                this.downloadTemplates = new Map(parsed);
            }
        } catch (error) {
            console.error('Failed to load download templates:', error);
            this.downloadTemplates = new Map();
        }
    }

    // Smart Notifications System Implementation
    setupSmartNotifications() {
        this.notificationSettings = {
            enabled: true,
            desktop: true,
            sound: true,
            progress: true,
            completion: true,
            errors: true,
            scheduled: true
        };

        this.loadNotificationSettings();
        this.requestNotificationPermission();
        this.createNotificationSettingsModal();
        this.setupNotificationQueue();
    }

    async requestNotificationPermission() {
        if ('Notification' in window) {
            if (Notification.permission === 'default') {
                const permission = await Notification.requestPermission();
                if (permission === 'granted') {
                    this.showNotification('Desktop notifications enabled!', 'success');
                } else {
                    console.log('Desktop notifications permission denied');
                }
            }
        }
    }

    createNotificationSettingsModal() {
        const notificationModal = document.createElement('div');
        notificationModal.id = 'notification-settings-modal';
        notificationModal.className = 'modal hidden';

        notificationModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>🔔 Notification Settings</h2>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="notification-settings">
                        <div class="setting-group">
                            <h3>General Settings</h3>
                            <label class="setting-item">
                                <input type="checkbox" id="notifications-enabled" checked>
                                <span>Enable Notifications</span>
                            </label>
                            <label class="setting-item">
                                <input type="checkbox" id="desktop-notifications" checked>
                                <span>Desktop Notifications</span>
                            </label>
                            <label class="setting-item">
                                <input type="checkbox" id="notification-sound" checked>
                                <span>Sound Alerts</span>
                            </label>
                        </div>

                        <div class="setting-group">
                            <h3>Notification Types</h3>
                            <label class="setting-item">
                                <input type="checkbox" id="progress-notifications" checked>
                                <span>Progress Updates (every 25%)</span>
                            </label>
                            <label class="setting-item">
                                <input type="checkbox" id="completion-notifications" checked>
                                <span>Download Completion</span>
                            </label>
                            <label class="setting-item">
                                <input type="checkbox" id="error-notifications" checked>
                                <span>Error Alerts</span>
                            </label>
                            <label class="setting-item">
                                <input type="checkbox" id="scheduled-notifications" checked>
                                <span>Scheduled Download Alerts</span>
                            </label>
                        </div>

                        <div class="setting-group">
                            <h3>Test Notifications</h3>
                            <button class="btn-secondary" onclick="window.app.testNotification('info')">Test Info</button>
                            <button class="btn-secondary" onclick="window.app.testNotification('success')">Test Success</button>
                            <button class="btn-secondary" onclick="window.app.testNotification('warning')">Test Warning</button>
                            <button class="btn-secondary" onclick="window.app.testNotification('error')">Test Error</button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="window.app.closeNotificationSettingsModal()">Cancel</button>
                    <button class="btn-primary" onclick="window.app.saveNotificationSettings()">Save Settings</button>
                </div>
            </div>
        `;

        document.body.appendChild(notificationModal);
        this.setupModalHandlers();
        this.setupNotificationSettingsHandlers();
    }

    setupNotificationSettingsHandlers() {
        // Load current settings into form
        document.getElementById('notifications-enabled').checked = this.notificationSettings.enabled;
        document.getElementById('desktop-notifications').checked = this.notificationSettings.desktop;
        document.getElementById('notification-sound').checked = this.notificationSettings.sound;
        document.getElementById('progress-notifications').checked = this.notificationSettings.progress;
        document.getElementById('completion-notifications').checked = this.notificationSettings.completion;
        document.getElementById('error-notifications').checked = this.notificationSettings.errors;
        document.getElementById('scheduled-notifications').checked = this.notificationSettings.scheduled;
    }

    setupNotificationQueue() {
        this.notificationQueue = [];
        this.isProcessingNotifications = false;
        this.lastProgressNotification = {};
    }

    showSmartNotification(title, message, type = 'info', options = {}) {
        // Check if notifications are enabled
        if (!this.notificationSettings.enabled) return;

        // Check specific notification type settings
        if (type === 'progress' && !this.notificationSettings.progress) return;
        if (type === 'completion' && !this.notificationSettings.completion) return;
        if (type === 'error' && !this.notificationSettings.errors) return;
        if (type === 'scheduled' && !this.notificationSettings.scheduled) return;

        // Add to queue
        const notification = {
            id: Date.now().toString(),
            title,
            message,
            type,
            timestamp: new Date(),
            ...options
        };

        this.notificationQueue.push(notification);
        this.processNotificationQueue();

        // Show desktop notification if enabled
        if (this.notificationSettings.desktop && 'Notification' in window && Notification.permission === 'granted') {
            this.showDesktopNotification(title, message, type, options);
        }

        // Play sound if enabled
        if (this.notificationSettings.sound) {
            this.playNotificationSound(type);
        }

        // Show in-app notification
        this.showNotification(message, type);
    }

    showDesktopNotification(title, message, type, options = {}) {
        const iconMap = {
            info: '💡',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            progress: '📊',
            completion: '🎉',
            scheduled: '⏰'
        };

        const icon = iconMap[type] || '📥';

        const notification = new Notification(`${icon} ${title}`, {
            body: message,
            icon: options.icon || '../assets/icon.png',
            badge: '../assets/icon.png',
            tag: options.tag || `downloader-${type}`,
            requireInteraction: type === 'error' || options.requireInteraction,
            silent: !this.notificationSettings.sound,
            ...options
        });

        // Auto-close after delay (except for errors)
        if (type !== 'error') {
            setTimeout(() => {
                notification.close();
            }, options.duration || 5000);
        }

        // Handle click events
        notification.onclick = () => {
            window.focus();
            notification.close();
            if (options.onClick) {
                options.onClick();
            }
        };

        // Handle notification action clicks (for browsers that support it)
        if (options.actions && 'actions' in Notification.prototype) {
            notification.addEventListener('notificationclick', (event) => {
                const action = event.action;
                const data = options.data || {};

                switch (action) {
                    case 'fix':
                        if (data.downloadId && window.downloadManager && typeof window.downloadManager.showErrorSolution === 'function') {
                            window.downloadManager.showErrorSolution(data.downloadId);
                        }
                        break;
                    case 'retry-original':
                        if (data.downloadId && window.downloadManager && typeof window.downloadManager.retryWithOriginalFormat === 'function') {
                            window.downloadManager.retryWithOriginalFormat(data.downloadId);
                        }
                        break;
                    case 'retry':
                        if (data.downloadId && window.downloadManager && typeof window.downloadManager.retryDownload === 'function') {
                            window.downloadManager.retryDownload(data.downloadId);
                        }
                        break;
                }

                notification.close();
                window.focus();
            });
        }
    }

    playNotificationSound(type) {
        // Create audio context for notification sounds
        if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }

        // Different frequencies for different notification types
        const frequencies = {
            info: 800,
            success: 1000,
            warning: 600,
            error: 400,
            progress: 900,
            completion: 1200,
            scheduled: 700
        };

        const frequency = frequencies[type] || 800;
        const duration = type === 'error' ? 0.3 : 0.15;

        // Create and play tone
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        oscillator.type = 'sine';

        gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }

    processNotificationQueue() {
        if (this.isProcessingNotifications) return;
        this.isProcessingNotifications = true;

        // Process notifications with rate limiting
        const processNext = () => {
            if (this.notificationQueue.length === 0) {
                this.isProcessingNotifications = false;
                return;
            }

            const notification = this.notificationQueue.shift();

            // Add to notification history
            this.addToNotificationHistory(notification);

            // Continue processing after delay
            setTimeout(processNext, 500);
        };

        processNext();
    }

    addToNotificationHistory(notification) {
        if (!this.notificationHistory) {
            this.notificationHistory = [];
        }

        this.notificationHistory.unshift(notification);

        // Keep only last 50 notifications
        if (this.notificationHistory.length > 50) {
            this.notificationHistory = this.notificationHistory.slice(0, 50);
        }

        // Save to storage
        try {
            localStorage.setItem('notificationHistory', JSON.stringify(this.notificationHistory));
        } catch (error) {
            console.error('Failed to save notification history:', error);
        }
    }

    // Enhanced download progress notifications
    notifyDownloadProgress(downloadId, progress, speed, eta) {
        const progressPercent = Math.round(progress);

        // Only notify at 25%, 50%, 75% milestones to avoid spam
        const milestones = [25, 50, 75];
        const lastNotified = this.lastProgressNotification[downloadId] || 0;

        const shouldNotify = milestones.some(milestone =>
            progressPercent >= milestone && lastNotified < milestone
        );

        if (shouldNotify) {
            this.lastProgressNotification[downloadId] = progressPercent;

            const speedText = FormatHelpers.formatSpeed(speed);
            const etaText = FormatHelpers.formatETA(eta, speed);

            this.showSmartNotification(
                'Download Progress',
                `${progressPercent}% complete • ${speedText} • ETA: ${etaText}`,
                'progress',
                {
                    tag: `download-progress-${downloadId}`,
                    duration: 3000
                }
            );
        }
    }

    notifyDownloadComplete(downloadInfo) {
        const title = downloadInfo.title || 'Download Complete';
        const size = FormatHelpers.formatFileSize(downloadInfo.size);

        this.showSmartNotification(
            'Download Complete! 🎉',
            `${title} (${size})`,
            'completion',
            {
                tag: 'download-complete',
                duration: 8000,
                requireInteraction: false,
                onClick: () => {
                    // Open download folder
                    if (window.electronAPI && window.electronAPI.openPath) {
                        window.electronAPI.openPath(downloadInfo.path);
                    }
                }
            }
        );

        // Clear progress tracking
        delete this.lastProgressNotification[downloadInfo.id];
    }

    notifyDownloadError(error, downloadInfo) {
        const title = downloadInfo?.title || 'Download Failed';
        const errorType = downloadInfo?.errorType || 'general';
        const solution = downloadInfo?.solution;

        let notificationBody = `${title}: ${error}`;
        if (solution) {
            notificationBody += `\n💡 Solution: ${solution}`;
        }

        // Add action buttons for specific error types
        const actions = [];
        if (errorType === 'encoder' || errorType === 'conversion') {
            actions.push({
                action: 'fix',
                title: '🔧 Fix Issue',
                icon: '🔧'
            });
            actions.push({
                action: 'retry-original',
                title: '📁 Try Original Format',
                icon: '📁'
            });
        } else {
            actions.push({
                action: 'retry',
                title: '🔄 Retry',
                icon: '🔄'
            });
        }

        this.showSmartNotification(
            'Download Error ❌',
            notificationBody,
            'error',
            {
                tag: 'download-error',
                requireInteraction: true,
                duration: 15000,
                actions: actions,
                data: {
                    downloadId: downloadInfo?.id,
                    errorType: errorType
                }
            }
        );
    }

    notifyScheduledDownload(scheduledDownload) {
        const title = scheduledDownload.videoInfo?.title || 'Scheduled Download';
        const timeStr = scheduledDownload.scheduledTime.toLocaleString();

        this.showSmartNotification(
            'Scheduled Download Starting ⏰',
            `${title} (scheduled for ${timeStr})`,
            'scheduled',
            {
                tag: 'scheduled-download',
                duration: 5000
            }
        );
    }

    testNotification(type) {
        const messages = {
            info: 'This is an info notification',
            success: 'This is a success notification',
            warning: 'This is a warning notification',
            error: 'This is an error notification'
        };

        this.showSmartNotification(
            `Test ${type.charAt(0).toUpperCase() + type.slice(1)} Notification`,
            messages[type],
            type,
            { duration: 3000 }
        );
    }

    showNotificationSettingsModal() {
        this.setupNotificationSettingsHandlers();
        const modal = document.getElementById('notification-settings-modal');
        modal.classList.remove('hidden');
    }

    closeNotificationSettingsModal() {
        const modal = document.getElementById('notification-settings-modal');
        modal.classList.add('hidden');
    }

    saveNotificationSettings() {
        this.notificationSettings = {
            enabled: document.getElementById('notifications-enabled').checked,
            desktop: document.getElementById('desktop-notifications').checked,
            sound: document.getElementById('notification-sound').checked,
            progress: document.getElementById('progress-notifications').checked,
            completion: document.getElementById('completion-notifications').checked,
            errors: document.getElementById('error-notifications').checked,
            scheduled: document.getElementById('scheduled-notifications').checked
        };

        // Save to storage
        try {
            localStorage.setItem('notificationSettings', JSON.stringify(this.notificationSettings));
        } catch (error) {
            console.error('Failed to save notification settings:', error);
        }

        this.closeNotificationSettingsModal();
        this.showNotification('Notification settings saved', 'success');
    }

    loadNotificationSettings() {
        try {
            const data = localStorage.getItem('notificationSettings');
            if (data) {
                this.notificationSettings = { ...this.notificationSettings, ...JSON.parse(data) };
            }
        } catch (error) {
            console.error('Failed to load notification settings:', error);
        }
    }

    // Advanced Queue Management Implementation
    setupAdvancedQueueManagement() {
        this.queueSettings = {
            maxConcurrentDownloads: 3,
            autoOptimize: true,
            priorityMode: 'smart', // 'manual', 'smart', 'fifo'
            bandwidthLimit: 0, // 0 = unlimited
            pauseOnBattery: false,
            pauseOnMetered: false
        };

        this.loadQueueSettings();
        this.createQueueManagementModal();
        this.setupQueueOptimization();
    }

    createQueueManagementModal() {
        const queueModal = document.createElement('div');
        queueModal.id = 'queue-management-modal';
        queueModal.className = 'modal hidden';

        queueModal.innerHTML = `
            <div class="modal-content">
                <div class="modal-header">
                    <h2>⚡ Queue Management</h2>
                    <button class="modal-close">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="queue-management-tabs">
                        <button class="tab-button active" data-tab="settings">Settings</button>
                        <button class="tab-button" data-tab="priorities">Priorities</button>
                        <button class="tab-button" data-tab="optimization">Optimization</button>
                    </div>

                    <div class="tab-content">
                        <!-- Settings Tab -->
                        <div id="settings-tab" class="tab-panel active">
                            <div class="settings-section">
                                <h3>Download Limits</h3>
                                <div class="form-group">
                                    <label for="max-concurrent">Max Concurrent Downloads:</label>
                                    <input type="number" id="max-concurrent" class="form-control" min="1" max="10" value="3">
                                    <small>Recommended: 2-4 for optimal performance</small>
                                </div>

                                <div class="form-group">
                                    <label for="bandwidth-limit">Bandwidth Limit (MB/s):</label>
                                    <input type="number" id="bandwidth-limit" class="form-control" min="0" step="0.1" value="0">
                                    <small>0 = Unlimited</small>
                                </div>
                            </div>

                            <div class="settings-section">
                                <h3>Queue Behavior</h3>
                                <div class="form-group">
                                    <label for="priority-mode">Priority Mode:</label>
                                    <select id="priority-mode" class="form-control">
                                        <option value="smart">Smart (AI-optimized)</option>
                                        <option value="manual">Manual Priority</option>
                                        <option value="fifo">First In, First Out</option>
                                    </select>
                                </div>

                                <label class="setting-item">
                                    <input type="checkbox" id="auto-optimize">
                                    <span>Auto-optimize queue order</span>
                                </label>

                                <label class="setting-item">
                                    <input type="checkbox" id="pause-on-battery">
                                    <span>Pause downloads on battery power</span>
                                </label>

                                <label class="setting-item">
                                    <input type="checkbox" id="pause-on-metered">
                                    <span>Pause downloads on metered connection</span>
                                </label>
                            </div>
                        </div>

                        <!-- Priorities Tab -->
                        <div id="priorities-tab" class="tab-panel">
                            <div class="priorities-section">
                                <h3>Download Priorities</h3>
                                <div class="priority-explanation">
                                    <p>Drag downloads to reorder them or set priority levels:</p>
                                    <div class="priority-legend">
                                        <span class="priority-high">🔴 High Priority</span>
                                        <span class="priority-normal">🟢 Normal Priority</span>
                                        <span class="priority-low">🟡 Low Priority</span>
                                    </div>
                                </div>
                                <div id="queue-list" class="queue-list"></div>
                            </div>
                        </div>

                        <!-- Optimization Tab -->
                        <div id="optimization-tab" class="tab-panel">
                            <div class="optimization-section">
                                <h3>Smart Queue Optimization</h3>
                                <div class="optimization-stats">
                                    <div class="stat-item">
                                        <span class="stat-label">Queue Efficiency:</span>
                                        <span id="queue-efficiency" class="stat-value">85%</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Estimated Completion:</span>
                                        <span id="estimated-completion" class="stat-value">2h 15m</span>
                                    </div>
                                    <div class="stat-item">
                                        <span class="stat-label">Bandwidth Usage:</span>
                                        <span id="bandwidth-usage" class="stat-value">75%</span>
                                    </div>
                                </div>

                                <div class="optimization-actions">
                                    <button class="btn-primary" onclick="window.app.optimizeQueue()">
                                        🚀 Optimize Queue Now
                                    </button>
                                    <button class="btn-secondary" onclick="window.app.analyzeQueue()">
                                        📊 Analyze Queue
                                    </button>
                                </div>

                                <div class="optimization-suggestions">
                                    <h4>Optimization Suggestions</h4>
                                    <div id="optimization-suggestions-list"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn-secondary" onclick="window.app.closeQueueManagementModal()">Cancel</button>
                    <button class="btn-primary" onclick="window.app.saveQueueSettings()">Save Settings</button>
                </div>
            </div>
        `;

        document.body.appendChild(queueModal);
        this.setupQueueManagementHandlers();
        this.setupModalHandlers();
    }

    setupQueueManagementHandlers() {
        // Tab switching
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                const tabName = e.target.dataset.tab;
                this.switchQueueTab(tabName);
            });
        });

        // Load current settings
        document.getElementById('max-concurrent').value = this.queueSettings.maxConcurrentDownloads;
        document.getElementById('bandwidth-limit').value = this.queueSettings.bandwidthLimit;
        document.getElementById('priority-mode').value = this.queueSettings.priorityMode;
        document.getElementById('auto-optimize').checked = this.queueSettings.autoOptimize;
        document.getElementById('pause-on-battery').checked = this.queueSettings.pauseOnBattery;
        document.getElementById('pause-on-metered').checked = this.queueSettings.pauseOnMetered;
    }

    switchQueueTab(tabName) {
        // Hide all tab panels
        document.querySelectorAll('.tab-panel').forEach(panel => {
            panel.classList.remove('active');
        });

        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });

        // Show selected tab panel
        document.getElementById(`${tabName}-tab`).classList.add('active');
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Load tab-specific content
        if (tabName === 'priorities') {
            this.updateQueueList();
        } else if (tabName === 'optimization') {
            this.updateOptimizationStats();
        }
    }

    showQueueManagementModal() {
        this.updateQueueList();
        this.updateOptimizationStats();
        const modal = document.getElementById('queue-management-modal');
        modal.classList.remove('hidden');
    }

    closeQueueManagementModal() {
        const modal = document.getElementById('queue-management-modal');
        modal.classList.add('hidden');
    }

    saveQueueSettings() {
        this.queueSettings = {
            maxConcurrentDownloads: parseInt(document.getElementById('max-concurrent').value),
            bandwidthLimit: parseFloat(document.getElementById('bandwidth-limit').value),
            priorityMode: document.getElementById('priority-mode').value,
            autoOptimize: document.getElementById('auto-optimize').checked,
            pauseOnBattery: document.getElementById('pause-on-battery').checked,
            pauseOnMetered: document.getElementById('pause-on-metered').checked
        };

        // Save to storage
        try {
            localStorage.setItem('queueSettings', JSON.stringify(this.queueSettings));
        } catch (error) {
            console.error('Failed to save queue settings:', error);
        }

        // Apply settings immediately
        this.applyQueueSettings();

        this.closeQueueManagementModal();
        this.showNotification('Queue settings saved', 'success');
    }

    loadQueueSettings() {
        try {
            const data = localStorage.getItem('queueSettings');
            if (data) {
                this.queueSettings = { ...this.queueSettings, ...JSON.parse(data) };
            }
        } catch (error) {
            console.error('Failed to load queue settings:', error);
        }
    }

    applyQueueSettings() {
        // Apply concurrent download limit
        if (window.downloadManager) {
            window.downloadManager.maxConcurrentDownloads = this.queueSettings.maxConcurrentDownloads;
        }

        // Apply auto-optimization
        if (this.queueSettings.autoOptimize) {
            this.startAutoOptimization();
        } else {
            this.stopAutoOptimization();
        }

        // Check battery and connection status
        this.checkSystemConditions();
    }

    setupQueueOptimization() {
        this.optimizationInterval = null;
        this.queueAnalytics = {
            totalDownloads: 0,
            completedDownloads: 0,
            averageSpeed: 0,
            estimatedCompletion: 0
        };
    }

    startAutoOptimization() {
        if (this.optimizationInterval) return;

        // Run optimization every 30 seconds
        this.optimizationInterval = setInterval(() => {
            this.optimizeQueue();
        }, 30000);
    }

    stopAutoOptimization() {
        if (this.optimizationInterval) {
            clearInterval(this.optimizationInterval);
            this.optimizationInterval = null;
        }
    }

    optimizeQueue() {
        if (!window.downloadManager) return;

        const downloads = Array.from(window.downloadManager.downloads.values());
        const pendingDownloads = downloads.filter(d => d.status === 'pending');

        if (pendingDownloads.length === 0) return;

        // Smart optimization based on priority mode
        switch (this.queueSettings.priorityMode) {
            case 'smart':
                this.smartOptimizeQueue(pendingDownloads);
                break;
            case 'manual':
                // Respect manual priorities
                break;
            case 'fifo':
                // First in, first out - no reordering needed
                break;
        }

        this.updateOptimizationStats();
    }

    smartOptimizeQueue(downloads) {
        // Smart optimization algorithm
        const optimizedOrder = downloads.sort((a, b) => {
            // Priority factors (higher score = higher priority)
            let scoreA = 0;
            let scoreB = 0;

            // File size factor (smaller files first for quick wins)
            const sizeA = a.size || 0;
            const sizeB = b.size || 0;
            if (sizeA > 0 && sizeB > 0) {
                scoreA += sizeA < sizeB ? 10 : 0;
                scoreB += sizeB < sizeA ? 10 : 0;
            }

            // Manual priority factor
            const priorityMap = { high: 30, normal: 20, low: 10 };
            scoreA += priorityMap[a.priority] || 20;
            scoreB += priorityMap[b.priority] || 20;

            // Time factor (older downloads get slight priority)
            const ageA = Date.now() - (a.startTime || Date.now());
            const ageB = Date.now() - (b.startTime || Date.now());
            scoreA += Math.min(ageA / (1000 * 60 * 60), 5); // Max 5 points for age
            scoreB += Math.min(ageB / (1000 * 60 * 60), 5);

            // Format factor (audio-only downloads are typically faster)
            if (a.type === 'audio') scoreA += 5;
            if (b.type === 'audio') scoreB += 5;

            return scoreB - scoreA; // Higher score first
        });

        // Apply the optimized order (this would need integration with download manager)
        this.showNotification('Queue optimized for better performance', 'info');
    }

    analyzeQueue() {
        if (!window.downloadManager) return;

        const downloads = Array.from(window.downloadManager.downloads.values());
        const analysis = this.performQueueAnalysis(downloads);

        this.displayAnalysisResults(analysis);
    }

    performQueueAnalysis(downloads) {
        const pending = downloads.filter(d => d.status === 'pending');
        const active = downloads.filter(d => d.status === 'downloading');
        const completed = downloads.filter(d => d.status === 'completed');

        const totalSize = downloads.reduce((sum, d) => sum + (d.size || 0), 0);
        const completedSize = completed.reduce((sum, d) => sum + (d.size || 0), 0);
        const averageSpeed = active.reduce((sum, d) => sum + (d.speed || 0), 0) / Math.max(active.length, 1);

        const remainingSize = totalSize - completedSize;
        const estimatedTime = averageSpeed > 0 ? remainingSize / averageSpeed : 0;

        return {
            totalDownloads: downloads.length,
            pendingDownloads: pending.length,
            activeDownloads: active.length,
            completedDownloads: completed.length,
            totalSize: totalSize,
            completedSize: completedSize,
            remainingSize: remainingSize,
            averageSpeed: averageSpeed,
            estimatedCompletion: estimatedTime,
            efficiency: totalSize > 0 ? (completedSize / totalSize) * 100 : 0
        };
    }

    displayAnalysisResults(analysis) {
        const suggestions = [];

        if (analysis.activeDownloads > this.queueSettings.maxConcurrentDownloads) {
            suggestions.push('Consider reducing concurrent downloads for better performance');
        }

        if (analysis.averageSpeed < 100000) { // Less than 100 KB/s
            suggestions.push('Low download speeds detected. Check your internet connection');
        }

        if (analysis.pendingDownloads > 10) {
            suggestions.push('Large queue detected. Consider prioritizing important downloads');
        }

        if (analysis.efficiency < 50) {
            suggestions.push('Queue efficiency is low. Try optimizing the download order');
        }

        // Update UI with suggestions
        const suggestionsList = document.getElementById('optimization-suggestions-list');
        if (suggestionsList) {
            suggestionsList.innerHTML = suggestions.length > 0
                ? suggestions.map(s => `<div class="suggestion-item">💡 ${s}</div>`).join('')
                : '<div class="suggestion-item">✅ Queue is running optimally</div>';
        }

        this.showNotification('Queue analysis complete', 'info');
    }

    updateQueueList() {
        const container = document.getElementById('queue-list');
        if (!container || !window.downloadManager) return;

        const downloads = Array.from(window.downloadManager.downloads.values())
            .filter(d => d.status === 'pending' || d.status === 'downloading');

        if (downloads.length === 0) {
            container.innerHTML = '<p class="no-queue-items">No downloads in queue</p>';
            return;
        }

        container.innerHTML = downloads.map(download => `
            <div class="queue-item" data-download-id="${download.id}">
                <div class="queue-item-info">
                    <div class="queue-item-title">${download.title}</div>
                    <div class="queue-item-details">
                        ${download.quality} • ${download.format} • ${FormatHelpers.formatFileSize(download.size)}
                    </div>
                </div>
                <div class="queue-item-controls">
                    <select class="priority-select" onchange="window.app.updateDownloadPriority('${download.id}', this.value)">
                        <option value="low" ${download.priority === 'low' ? 'selected' : ''}>Low</option>
                        <option value="normal" ${download.priority === 'normal' ? 'selected' : ''}>Normal</option>
                        <option value="high" ${download.priority === 'high' ? 'selected' : ''}>High</option>
                    </select>
                    <button class="move-up-btn" onclick="window.app.moveDownloadUp('${download.id}')" title="Move Up">↑</button>
                    <button class="move-down-btn" onclick="window.app.moveDownloadDown('${download.id}')" title="Move Down">↓</button>
                </div>
            </div>
        `).join('');
    }

    updateOptimizationStats() {
        if (!window.downloadManager) return;

        const analysis = this.performQueueAnalysis(Array.from(window.downloadManager.downloads.values()));

        // Update stats display
        const efficiencyEl = document.getElementById('queue-efficiency');
        const completionEl = document.getElementById('estimated-completion');
        const bandwidthEl = document.getElementById('bandwidth-usage');

        if (efficiencyEl) efficiencyEl.textContent = `${Math.round(analysis.efficiency)}%`;
        if (completionEl) completionEl.textContent = FormatHelpers.formatETA(analysis.estimatedCompletion);
        if (bandwidthEl) bandwidthEl.textContent = `${Math.round((analysis.averageSpeed / 1000000) * 100)}%`;
    }

    updateDownloadPriority(downloadId, priority) {
        if (window.downloadManager && window.downloadManager.downloads.has(downloadId)) {
            const download = window.downloadManager.downloads.get(downloadId);
            download.priority = priority;

            // Re-optimize if auto-optimization is enabled
            if (this.queueSettings.autoOptimize) {
                setTimeout(() => this.optimizeQueue(), 100);
            }

            this.showNotification(`Priority updated to ${priority}`, 'info');
        }
    }

    moveDownloadUp(downloadId) {
        // Implementation would depend on download manager's queue structure
        this.showNotification('Download moved up in queue', 'info');
        this.updateQueueList();
    }

    moveDownloadDown(downloadId) {
        // Implementation would depend on download manager's queue structure
        this.showNotification('Download moved down in queue', 'info');
        this.updateQueueList();
    }

    checkSystemConditions() {
        // Check battery status
        if ('getBattery' in navigator) {
            navigator.getBattery().then(battery => {
                if (this.queueSettings.pauseOnBattery && !battery.charging) {
                    this.pauseDownloadsForBattery();
                }
            });
        }

        // Check connection type
        if ('connection' in navigator) {
            const connection = navigator.connection;
            if (this.queueSettings.pauseOnMetered && connection.saveData) {
                this.pauseDownloadsForMetered();
            }
        }
    }

    pauseDownloadsForBattery() {
        this.showSmartNotification(
            'Downloads Paused',
            'Downloads paused due to battery power',
            'warning',
            { duration: 5000 }
        );
    }

    pauseDownloadsForMetered() {
        this.showSmartNotification(
            'Downloads Paused',
            'Downloads paused due to metered connection',
            'warning',
            { duration: 5000 }
        );
    }

    // User Onboarding Experience Implementation
    setupUserOnboarding() {
        this.onboardingState = {
            isFirstTime: true,
            currentStep: 0,
            completedSteps: [],
            showTips: true,
            tourActive: false
        };

        this.loadOnboardingState();
        this.createOnboardingElements();

        // Check if this is first time user
        if (this.onboardingState.isFirstTime) {
            setTimeout(() => this.startWelcomeTour(), 1000);
        }
    }

    loadOnboardingState() {
        try {
            const data = localStorage.getItem('onboardingState');
            if (data) {
                this.onboardingState = { ...this.onboardingState, ...JSON.parse(data) };
            }
        } catch (error) {
            console.error('Failed to load onboarding state:', error);
        }
    }

    saveOnboardingState() {
        try {
            localStorage.setItem('onboardingState', JSON.stringify(this.onboardingState));
        } catch (error) {
            console.error('Failed to save onboarding state:', error);
        }
    }

    createOnboardingElements() {
        this.createWelcomeModal();
        this.createTourOverlay();
        this.createHelpButton();
        this.createTipSystem();
    }

    createWelcomeModal() {
        const welcomeModal = document.createElement('div');
        welcomeModal.id = 'welcome-modal';
        welcomeModal.className = 'modal onboarding-modal';

        welcomeModal.innerHTML = `
            <div class="modal-content welcome-content">
                <div class="welcome-header">
                    <div class="welcome-icon">🎉</div>
                    <h1>Welcome to Downloader Pro!</h1>
                    <p>Your ultimate video downloading companion</p>
                </div>
                <div class="welcome-body">
                    <div class="welcome-features">
                        <div class="feature-item">
                            <div class="feature-icon">🚀</div>
                            <h3>Lightning Fast</h3>
                            <p>Download videos from multiple platforms at incredible speeds</p>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🎯</div>
                            <h3>Smart Features</h3>
                            <p>AI-powered suggestions, scheduling, and queue management</p>
                        </div>
                        <div class="feature-item">
                            <div class="feature-icon">🎨</div>
                            <h3>Beautiful Interface</h3>
                            <p>Intuitive design with powerful features at your fingertips</p>
                        </div>
                    </div>
                    <div class="welcome-actions">
                        <button class="btn-secondary" onclick="window.app.skipTour()">Skip Tour</button>
                        <button class="btn-primary" onclick="window.app.startWelcomeTour()">Take the Tour</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(welcomeModal);
    }

    createTourOverlay() {
        const tourOverlay = document.createElement('div');
        tourOverlay.id = 'tour-overlay';
        tourOverlay.className = 'tour-overlay hidden';

        tourOverlay.innerHTML = `
            <div class="tour-backdrop"></div>
            <div class="tour-spotlight"></div>
            <div class="tour-tooltip">
                <div class="tour-content">
                    <h3 class="tour-title"></h3>
                    <p class="tour-description"></p>
                    <div class="tour-progress">
                        <span class="tour-step-counter"></span>
                        <div class="tour-progress-bar">
                            <div class="tour-progress-fill"></div>
                        </div>
                    </div>
                </div>
                <div class="tour-controls">
                    <button class="btn-secondary tour-skip" onclick="window.app.skipTour()">Skip</button>
                    <button class="btn-secondary tour-prev" onclick="window.app.previousTourStep()">Previous</button>
                    <button class="btn-primary tour-next" onclick="window.app.nextTourStep()">Next</button>
                    <button class="btn-primary tour-finish hidden" onclick="window.app.finishTour()">Finish</button>
                </div>
            </div>
        `;

        document.body.appendChild(tourOverlay);
    }

    createHelpButton() {
        const helpButton = document.createElement('button');
        helpButton.id = 'help-button';
        helpButton.className = 'help-button';
        helpButton.title = 'Help & Tips';
        helpButton.innerHTML = '❓';
        helpButton.onclick = () => this.showHelpMenu();

        document.body.appendChild(helpButton);
    }

    createTipSystem() {
        const tipContainer = document.createElement('div');
        tipContainer.id = 'tip-container';
        tipContainer.className = 'tip-container hidden';

        tipContainer.innerHTML = `
            <div class="tip-content">
                <div class="tip-icon">💡</div>
                <div class="tip-text"></div>
                <button class="tip-close" onclick="window.app.closeTip()">×</button>
            </div>
        `;

        document.body.appendChild(tipContainer);
    }

    startWelcomeTour() {
        if (this.onboardingState.tourActive) return;

        this.onboardingState.tourActive = true;
        this.onboardingState.currentStep = 0;

        // Hide welcome modal
        const welcomeModal = document.getElementById('welcome-modal');
        if (welcomeModal) welcomeModal.classList.add('hidden');

        // Define tour steps
        this.tourSteps = [
            {
                target: '#url-input',
                title: 'Enter Video URL',
                description: 'Paste any video URL here. We support YouTube, Facebook, Instagram, TikTok, and many more platforms!',
                position: 'bottom'
            },
            {
                target: '.download-options',
                title: 'Download Options',
                description: 'Choose your preferred quality, format, and destination. Our smart suggestions will help you pick the best settings.',
                position: 'top'
            },
            {
                target: '#download-button',
                title: 'Start Download',
                description: 'Click here to start downloading, or use Ctrl+Enter for quick downloads!',
                position: 'top'
            },
            {
                target: '#schedule-button',
                title: 'Schedule Downloads',
                description: 'Schedule downloads for later, perfect for managing bandwidth during off-peak hours.',
                position: 'top'
            },
            {
                target: '#notifications-button',
                title: 'Smart Notifications',
                description: 'Get desktop notifications for download progress, completion, and errors. Fully customizable!',
                position: 'top'
            },
            {
                target: '#queue-management-button',
                title: 'Queue Management',
                description: 'Manage your download queue with priorities, reordering, and smart optimization.',
                position: 'top'
            },
            {
                target: '.downloads-section',
                title: 'Download Manager',
                description: 'Monitor all your downloads here. Pause, resume, or cancel downloads as needed.',
                position: 'top'
            }
        ];

        this.showTourStep(0);
    }

    showTourStep(stepIndex) {
        // Ensure tourSteps is initialized
        if (!this.tourSteps || !Array.isArray(this.tourSteps)) {
            console.warn('Tour steps not properly initialized');
            this.finishTour();
            return;
        }

        if (stepIndex >= this.tourSteps.length) {
            this.finishTour();
            return;
        }

        const step = this.tourSteps[stepIndex];
        const target = document.querySelector(step.target);

        if (!target) {
            // Skip this step if target doesn't exist
            this.nextTourStep();
            return;
        }

        // Show tour overlay
        const overlay = document.getElementById('tour-overlay');
        overlay.classList.remove('hidden');

        // Position spotlight on target
        this.positionSpotlight(target);

        // Update tooltip content
        this.updateTourTooltip(step, stepIndex);

        // Position tooltip
        this.positionTourTooltip(target, step.position);

        this.onboardingState.currentStep = stepIndex;
    }

    positionSpotlight(target) {
        const rect = target.getBoundingClientRect();
        const spotlight = document.querySelector('.tour-spotlight');

        spotlight.style.left = `${rect.left - 10}px`;
        spotlight.style.top = `${rect.top - 10}px`;
        spotlight.style.width = `${rect.width + 20}px`;
        spotlight.style.height = `${rect.height + 20}px`;
    }

    updateTourTooltip(step, stepIndex) {
        document.querySelector('.tour-title').textContent = step.title;
        document.querySelector('.tour-description').textContent = step.description;
        document.querySelector('.tour-step-counter').textContent = `Step ${stepIndex + 1} of ${this.tourSteps.length}`;

        // Update progress bar
        const progress = ((stepIndex + 1) / this.tourSteps.length) * 100;
        document.querySelector('.tour-progress-fill').style.width = `${progress}%`;

        // Show/hide navigation buttons
        const prevBtn = document.querySelector('.tour-prev');
        const nextBtn = document.querySelector('.tour-next');
        const finishBtn = document.querySelector('.tour-finish');

        prevBtn.style.display = stepIndex > 0 ? 'inline-block' : 'none';

        if (stepIndex === this.tourSteps.length - 1) {
            nextBtn.classList.add('hidden');
            finishBtn.classList.remove('hidden');
        } else {
            nextBtn.classList.remove('hidden');
            finishBtn.classList.add('hidden');
        }
    }

    positionTourTooltip(target, position) {
        const rect = target.getBoundingClientRect();
        const tooltip = document.querySelector('.tour-tooltip');
        const tooltipRect = tooltip.getBoundingClientRect();

        let left, top;

        switch (position) {
            case 'top':
                left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                top = rect.top - tooltipRect.height - 20;
                break;
            case 'bottom':
                left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                top = rect.bottom + 20;
                break;
            case 'left':
                left = rect.left - tooltipRect.width - 20;
                top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
                break;
            case 'right':
                left = rect.right + 20;
                top = rect.top + (rect.height / 2) - (tooltipRect.height / 2);
                break;
            default:
                left = rect.left + (rect.width / 2) - (tooltipRect.width / 2);
                top = rect.bottom + 20;
        }

        // Ensure tooltip stays within viewport
        left = Math.max(20, Math.min(left, window.innerWidth - tooltipRect.width - 20));
        top = Math.max(20, Math.min(top, window.innerHeight - tooltipRect.height - 20));

        tooltip.style.left = `${left}px`;
        tooltip.style.top = `${top}px`;
    }

    nextTourStep() {
        this.showTourStep(this.onboardingState.currentStep + 1);
    }

    previousTourStep() {
        if (this.onboardingState.currentStep > 0) {
            this.showTourStep(this.onboardingState.currentStep - 1);
        }
    }

    skipTour() {
        this.finishTour();
    }

    finishTour() {
        this.onboardingState.tourActive = false;
        this.onboardingState.isFirstTime = false;

        // Safely handle tourSteps array
        const tourStepsLength = this.tourSteps ? this.tourSteps.length : 7; // Default to 7 steps
        this.onboardingState.completedSteps = [...Array(tourStepsLength).keys()];

        // Hide tour elements
        const overlay = document.getElementById('tour-overlay');
        const welcomeModal = document.getElementById('welcome-modal');

        if (overlay) overlay.classList.add('hidden');
        if (welcomeModal) welcomeModal.classList.add('hidden');

        // Save state
        this.saveOnboardingState();

        // Show completion message
        this.showSmartNotification(
            'Welcome Tour Complete! 🎉',
            'You\'re all set! Try pasting a video URL to get started.',
            'success',
            { duration: 6000 }
        );

        // Show first helpful tip
        setTimeout(() => this.showRandomTip(), 2000);
    }

    showHelpMenu() {
        const helpMenu = document.createElement('div');
        helpMenu.className = 'help-menu';
        helpMenu.innerHTML = `
            <div class="help-menu-content">
                <h3>Help & Tips</h3>
                <div class="help-menu-items">
                    <button onclick="window.app.restartTour()">🎯 Restart Tour</button>
                    <button onclick="window.app.showRandomTip()">💡 Show Tip</button>
                    <button onclick="window.app.showKeyboardShortcuts()">⌨️ Keyboard Shortcuts</button>
                    <button onclick="window.app.showFeatureGuide()">📖 Feature Guide</button>
                    <button onclick="window.app.toggleTips()">🔔 Toggle Tips</button>
                </div>
            </div>
        `;

        // Position near help button
        const helpButton = document.getElementById('help-button');
        const rect = helpButton.getBoundingClientRect();
        helpMenu.style.position = 'fixed';
        helpMenu.style.right = '20px';
        helpMenu.style.top = `${rect.bottom + 10}px`;

        document.body.appendChild(helpMenu);

        // Remove on click outside
        setTimeout(() => {
            document.addEventListener('click', function removeMenu(e) {
                if (!helpMenu.contains(e.target) && e.target !== helpButton) {
                    helpMenu.remove();
                    document.removeEventListener('click', removeMenu);
                }
            });
        }, 100);
    }

    restartTour() {
        this.onboardingState.isFirstTime = true;
        this.onboardingState.currentStep = 0;
        this.startWelcomeTour();
    }

    showRandomTip() {
        if (!this.onboardingState.showTips) return;

        const tips = [
            'Tip: Use Ctrl+V to quickly paste and process URLs!',
            'Tip: Right-click the download button for quick actions.',
            'Tip: Schedule downloads during off-peak hours to save bandwidth.',
            'Tip: Use templates to save your favorite download settings.',
            'Tip: Drag and drop URLs or text files directly into the app.',
            'Tip: Press F1 to see all keyboard shortcuts.',
            'Tip: The smart suggestions help you choose optimal settings.',
            'Tip: You can prioritize downloads in the queue management.',
            'Tip: Desktop notifications keep you updated on download progress.',
            'Tip: Use audio-only downloads for music and podcasts.',
            'Tip: If downloads fail with encoder errors, try the "Fix Issue" button for solutions.',
            'Tip: Original format downloads are faster and don\'t require FFmpeg.'
        ];

        const randomTip = tips[Math.floor(Math.random() * tips.length)];
        this.showTip(randomTip);
    }

    showTip(message) {
        const tipContainer = document.getElementById('tip-container');
        const tipText = tipContainer.querySelector('.tip-text');

        tipText.textContent = message;
        tipContainer.classList.remove('hidden');

        // Auto-hide after 8 seconds
        setTimeout(() => {
            tipContainer.classList.add('hidden');
        }, 8000);
    }

    closeTip() {
        const tipContainer = document.getElementById('tip-container');
        tipContainer.classList.add('hidden');
    }

    toggleTips() {
        this.onboardingState.showTips = !this.onboardingState.showTips;
        this.saveOnboardingState();

        const status = this.onboardingState.showTips ? 'enabled' : 'disabled';
        this.showNotification(`Tips ${status}`, 'info');
    }

    showFeatureGuide() {
        const features = [
            '🎯 Smart Suggestions: AI-powered download recommendations',
            '📅 Scheduler: Time your downloads for optimal bandwidth usage',
            '📋 Templates: Save and reuse your favorite settings',
            '🔔 Notifications: Stay updated with desktop alerts',
            '⚡ Queue Management: Prioritize and optimize your downloads',
            '⌨️ Keyboard Shortcuts: Power user controls',
            '🎨 Drag & Drop: Easy URL input and file handling'
        ];

        const guide = features.join('\n');
        this.showSmartNotification(
            'Feature Guide 📖',
            guide,
            'info',
            { duration: 10000 }
        );
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    console.log('DOM loaded, initializing app...');
    window.app = new DownloaderApp();
    console.log('App initialized:', window.app);

    // Dispatch a custom event to notify other scripts that the app is ready
    window.dispatchEvent(new CustomEvent('app-ready'));
});
