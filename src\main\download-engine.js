const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs-extra');
const { EventEmitter } = require('events');

class DownloadEngine extends EventEmitter {
    constructor() {
        super();
        this.activeDownloads = new Map();
        this.ytDlpPath = this.getYtDlpPath();
    }

    getYtDlpPath() {
        // In production, yt-dlp would be bundled with the app
        // For now, we'll assume it's available in PATH
        return process.platform === 'win32' ? 'yt-dlp.exe' : 'yt-dlp';
    }

    detectPlatformFromUrl(url) {
        if (!url) return 'unknown';

        const urlLower = url.toLowerCase();

        if (urlLower.includes('youtube.com') || urlLower.includes('youtu.be')) {
            return 'youtube';
        } else if (urlLower.includes('facebook.com') || urlLower.includes('fb.watch')) {
            return 'facebook';
        } else if (urlLower.includes('instagram.com')) {
            return 'instagram';
        } else if (urlLower.includes('tiktok.com')) {
            return 'tiktok';
        } else if (urlLower.includes('twitter.com') || urlLower.includes('x.com')) {
            return 'twitter';
        } else if (urlLower.includes('pinterest.com') || urlLower.includes('pin.it')) {
            return 'pinterest';
        }

        return 'unknown';
    }

    buildPinterestOutputPath(url) {
        try {
            const urlObj = new URL(url);
            const pathname = urlObj.pathname;

            // Check for short URLs first
            if (urlObj.hostname.includes('pin.it')) {
                // Short URL: https://pin.it/abc123 - treat as individual pin
                return 'Pinterest/Pins/%(uploader)s - %(title)s.%(ext)s';
            }

            // Detect Pinterest content type from URL structure
            if (pathname.includes('/pin/')) {
                // Individual pin: https://pinterest.com/pin/123456789/
                return 'Pinterest/Pins/%(uploader)s - %(title)s.%(ext)s';
            } else if (pathname.match(/\/[^\/]+\/[^\/]+\/?$/)) {
                // Board: https://pinterest.com/user/board-name/
                const pathParts = pathname.split('/').filter(part => part);
                if (pathParts.length >= 2) {
                    const username = pathParts[0];
                    const boardName = pathParts[1];
                    return `Pinterest/Boards/${username}/${boardName}/%(title)s.%(ext)s`;
                }
                return 'Pinterest/Boards/%(uploader)s - %(title)s.%(ext)s';
            } else if (pathname.match(/\/[^\/]+\/?$/)) {
                // User profile: https://pinterest.com/username/
                const username = pathname.split('/').filter(part => part)[0];
                return `Pinterest/Profiles/${username}/%(title)s.%(ext)s`;
            }

            // Fallback for any other Pinterest URL structure
            return 'Pinterest/Other/%(uploader)s - %(title)s.%(ext)s';
        } catch (error) {
            console.warn('Error parsing Pinterest URL for path organization:', error);
            // Fallback to basic Pinterest folder
            return 'Pinterest/%(uploader)s - %(title)s.%(ext)s';
        }
    }

    detectPinterestError(errorText, url) {
        // Only check Pinterest errors for Pinterest URLs
        if (!url || !this.detectPlatformFromUrl(url) === 'pinterest') {
            return null;
        }

        const errorLower = errorText.toLowerCase();

        // Private pin error
        if (errorLower.includes('private') || errorLower.includes('not available') || errorLower.includes('access denied')) {
            return {
                type: 'private_content',
                message: 'This Pinterest pin is private or requires authentication. Please check if the pin is publicly accessible.'
            };
        }

        // Deleted/removed content
        if (errorLower.includes('not found') || errorLower.includes('404') || errorLower.includes('removed')) {
            return {
                type: 'content_not_found',
                message: 'This Pinterest pin has been deleted or is no longer available.'
            };
        }

        // Rate limiting
        if (errorLower.includes('rate limit') || errorLower.includes('too many requests') || errorLower.includes('429')) {
            return {
                type: 'rate_limited',
                message: 'Pinterest is rate limiting requests. Please wait a few minutes before trying again.'
            };
        }

        // Authentication required
        if (errorLower.includes('login') || errorLower.includes('authentication') || errorLower.includes('sign in')) {
            return {
                type: 'authentication_required',
                message: 'This Pinterest content requires authentication. Some pins may only be accessible when logged in.'
            };
        }

        // Network/connection issues
        if (errorLower.includes('network') || errorLower.includes('connection') || errorLower.includes('timeout')) {
            return {
                type: 'network_error',
                message: 'Network error while accessing Pinterest. Please check your internet connection and try again.'
            };
        }

        // Pinterest-specific extractor errors
        if (errorLower.includes('pinterest') && (errorLower.includes('error') || errorLower.includes('failed'))) {
            return {
                type: 'pinterest_extractor_error',
                message: 'Pinterest extractor encountered an error. This may be due to changes in Pinterest\'s website structure.'
            };
        }

        return null;
    }

    async checkYtDlpAvailability() {
        return new Promise((resolve) => {
            let output = '';
            let errorOutput = '';

            const process = spawn(this.ytDlpPath, ['--version'], { stdio: 'pipe' });

            process.stdout.on('data', (data) => {
                output += data.toString();
            });

            process.stderr.on('data', (data) => {
                errorOutput += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    console.log(`yt-dlp is available, version: ${output.trim()}`);
                    resolve(true);
                } else {
                    console.error(`yt-dlp check failed with code ${code}:`, errorOutput);
                    resolve(false);
                }
            });

            process.on('error', (error) => {
                console.error('yt-dlp availability check error:', error.message);
                resolve(false);
            });

            // Timeout after 10 seconds
            setTimeout(() => {
                process.kill();
                console.error('yt-dlp availability check timed out');
                resolve(false);
            }, 10000);
        });
    }

    // Enhanced method to get detailed yt-dlp status
    async getYtDlpStatus() {
        try {
            const isAvailable = await this.checkYtDlpAvailability();

            if (!isAvailable) {
                return {
                    available: false,
                    version: null,
                    path: this.ytDlpPath,
                    error: 'yt-dlp not found or not executable',
                    installInstructions: [
                        'Install yt-dlp using one of these methods:',
                        '1. Run "npm run install-ytdlp" in the project directory',
                        '2. Install via pip: pip install yt-dlp',
                        '3. Download from: https://github.com/yt-dlp/yt-dlp/releases',
                        '4. Make sure yt-dlp is in your system PATH'
                    ]
                };
            }

            // Get version info
            return new Promise((resolve) => {
                let versionOutput = '';
                const process = spawn(this.ytDlpPath, ['--version'], { stdio: 'pipe' });

                process.stdout.on('data', (data) => {
                    versionOutput += data.toString();
                });

                process.on('close', (code) => {
                    resolve({
                        available: true,
                        version: versionOutput.trim(),
                        path: this.ytDlpPath,
                        error: null,
                        installInstructions: null
                    });
                });

                process.on('error', () => {
                    resolve({
                        available: false,
                        version: null,
                        path: this.ytDlpPath,
                        error: 'Failed to get version information',
                        installInstructions: null
                    });
                });
            });

        } catch (error) {
            return {
                available: false,
                version: null,
                path: this.ytDlpPath,
                error: error.message,
                installInstructions: null
            };
        }
    }

    async getVideoInfo(url) {
        return new Promise((resolve, reject) => {
            const args = [
                '--dump-json',
                '--no-playlist',
                url
            ];

            const process = spawn(this.ytDlpPath, args, { stdio: 'pipe' });
            let output = '';
            let error = '';

            process.stdout.on('data', (data) => {
                output += data.toString();
            });

            process.stderr.on('data', (data) => {
                error += data.toString();
            });

            process.on('close', (code) => {
                if (code === 0) {
                    try {
                        const info = JSON.parse(output);
                        resolve({
                            title: info.title || 'Unknown Title',
                            description: info.description || '',
                            thumbnail: info.thumbnail || '',
                            duration: info.duration || 0,
                            uploader: info.uploader || info.channel || '',
                            view_count: info.view_count || 0,
                            formats: info.formats || [],
                            webpage_url: info.webpage_url || url
                        });
                    } catch (parseError) {
                        reject(new Error('Failed to parse video information'));
                    }
                } else {
                    reject(new Error(error || 'Failed to get video information'));
                }
            });

            process.on('error', (err) => {
                reject(new Error(`Failed to execute yt-dlp: ${err.message}`));
            });
        });
    }

    async startDownload(downloadId, options) {
        const { url, type, quality, format, destination } = options;

        console.log(`Starting download ${downloadId} with options:`, {
            url: url,
            type: type,
            quality: quality,
            format: format,
            destination: destination
        });

        // Ensure destination directory exists
        await fs.ensureDir(destination);

        const args = this.buildDownloadArgs(options);
        console.log(`yt-dlp command for download ${downloadId}:`, this.ytDlpPath, args.join(' '));

        const process = spawn(this.ytDlpPath, args, {
            stdio: 'pipe',
            cwd: destination
        });

        const downloadInfo = {
            id: downloadId,
            process: process,
            options: options,
            startTime: Date.now(),
            lastSize: 0,
            paused: false
        };

        this.activeDownloads.set(downloadId, downloadInfo);

        // Handle process output
        this.setupProcessHandlers(downloadId, process);

        return downloadId;
    }

    buildDownloadArgs(options) {
        const { url, type, quality, format, destination } = options;
        const args = [];

        // Detect platform for platform-specific handling
        const platform = this.detectPlatformFromUrl(url);

        // Basic options
        args.push('--no-playlist');
        args.push('--progress');
        args.push('--newline');

        // Platform-specific rate limiting
        if (platform === 'pinterest') {
            // Pinterest rate limiting to avoid being blocked
            args.push('--sleep-interval', '1');
            args.push('--max-sleep-interval', '3');
        }

        // Output template with platform-specific subfolder
        if (platform === 'pinterest') {
            const pinterestPath = this.buildPinterestOutputPath(url);
            args.push('-o', pinterestPath);
        } else {
            args.push('-o', '%(title)s.%(ext)s');
        }

        // Quality selection
        if (type === 'audio') {
            args.push('--extract-audio');
            if (format !== 'auto') {
                args.push('--audio-format', format);
            }
            args.push('--audio-quality', this.mapQualityToAudio(quality));
        } else {
            // Video download - Enhanced quality selection
            const formatSelector = this.buildVideoFormatSelector(quality, format, platform);
            args.push('-f', formatSelector);

            // Only use recode if we need format conversion and it won't interfere with quality
            if (format !== 'auto' && this.shouldRecodeVideo(format, quality)) {
                args.push('--recode-video', format);
            }
        }

        // Subtitles
        if (options.downloadSubtitles) {
            args.push('--write-subs');
            if (options.subtitleLanguage && options.subtitleLanguage !== 'all') {
                args.push('--sub-langs', options.subtitleLanguage);
            }
        }

        // Platform-specific metadata preservation
        if (platform === 'pinterest') {
            // Write metadata for Pinterest pins
            args.push('--write-info-json');
            args.push('--write-description');
            args.push('--write-thumbnail');

            // Pinterest-specific error handling
            args.push('--ignore-errors');
            args.push('--no-abort-on-error');
        }

        // URL
        args.push(url);

        // Debug: Log the complete command
        console.log('yt-dlp command args:', args);

        return args;
    }

    buildVideoFormatSelector(quality, format, platform = 'unknown') {
        // Pinterest-specific format selection
        if (platform === 'pinterest') {
            // Pinterest videos are often in different formats, be more flexible
            switch (quality) {
                case 'best':
                    return 'best[ext=mp4]/best[ext=webm]/best';
                case '720p':
                    return 'best[height<=720][ext=mp4]/best[height<=720][ext=webm]/best[height<=720]';
                case '480p':
                    return 'best[height<=480][ext=mp4]/best[height<=480][ext=webm]/best[height<=480]';
                case '360p':
                    return 'best[height<=360][ext=mp4]/best[height<=360][ext=webm]/best[height<=360]';
                default:
                    return 'best[ext=mp4]/best[ext=webm]/best';
            }
        }

        // Enhanced format selection for other platforms
        switch (quality) {
            case 'best':
                // Best quality: prefer highest resolution with best audio
                // This ensures we get the highest quality available, combining video and audio if needed
                return 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/bestvideo+bestaudio/best[ext=mp4]/best';

            case 'worst':
                // Lowest quality
                return 'worst[ext=mp4]/worst';

            case '8k':
                // 8K (7680x4320) or closest available
                return 'bestvideo[height<=4320][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=4320]+bestaudio/best[height<=4320][ext=mp4]/best[height<=4320]';

            case '4k':
                // 4K (3840x2160) or closest available
                return 'bestvideo[height<=2160][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=2160]+bestaudio/best[height<=2160][ext=mp4]/best[height<=2160]';

            case '2k':
                // 2K (2560x1440) or closest available
                return 'bestvideo[height<=1440][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=1440]+bestaudio/best[height<=1440][ext=mp4]/best[height<=1440]';

            case '1080p':
                // 1080p (1920x1080) or closest available
                return 'bestvideo[height<=1080][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=1080]+bestaudio/best[height<=1080][ext=mp4]/best[height<=1080]';

            case '720p':
                // 720p (1280x720) or closest available
                return 'bestvideo[height<=720][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=720]+bestaudio/best[height<=720][ext=mp4]/best[height<=720]';

            case '480p':
                // 480p (854x480) or closest available
                return 'bestvideo[height<=480][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=480]+bestaudio/best[height<=480][ext=mp4]/best[height<=480]';

            case '360p':
                // 360p (640x360) or closest available
                return 'bestvideo[height<=360][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=360]+bestaudio/best[height<=360][ext=mp4]/best[height<=360]';

            case '240p':
                // 240p (426x240) or closest available
                return 'bestvideo[height<=240][ext=mp4]+bestaudio[ext=m4a]/bestvideo[height<=240]+bestaudio/best[height<=240][ext=mp4]/best[height<=240]';

            default:
                // Fallback to best quality
                console.warn('Unknown quality setting:', quality, 'falling back to best');
                return 'bestvideo[ext=mp4]+bestaudio[ext=m4a]/bestvideo+bestaudio/best[ext=mp4]/best';
        }
    }

    shouldRecodeVideo(format, quality) {
        // Only recode if we're not already getting the desired format
        // and if it won't significantly impact quality
        const noRecodeFormats = ['mp4', 'auto'];
        const highQualitySettings = ['best', '8k', '4k', '2k', '1080p'];

        // Don't recode for high quality settings with mp4 format to preserve quality
        if (highQualitySettings.includes(quality) && format === 'mp4') {
            return false;
        }

        // Don't recode if format is auto or already mp4
        if (noRecodeFormats.includes(format)) {
            return false;
        }

        return true;
    }

    mapQualityToAudio(quality) {
        const qualityMap = {
            'best': '0',
            '320': '320K',
            '256': '256K',
            '192': '192K',
            '128': '128K',
            'worst': '9'
        };
        return qualityMap[quality] || '0';
    }

    setupProcessHandlers(downloadId, process) {
        let output = '';
        let error = '';

        process.stdout.on('data', (data) => {
            const text = data.toString();
            output += text;
            this.parseProgress(downloadId, text);
        });

        process.stderr.on('data', (data) => {
            const text = data.toString();
            error += text;

            // Check for Pinterest-specific errors
            const pinterestError = this.detectPinterestError(text, downloadInfo.options.url);
            if (pinterestError) {
                this.emit('download-error', {
                    id: downloadId,
                    error: pinterestError.message,
                    errorType: pinterestError.type,
                    platform: 'pinterest'
                });
                return;
            }

            this.parseProgress(downloadId, text);
        });

        process.on('close', (code) => {
            const downloadInfo = this.activeDownloads.get(downloadId);
            if (!downloadInfo) return;

            if (code === 0) {
                this.emit('download-complete', {
                    id: downloadId,
                    filePath: this.getOutputFilePath(downloadInfo.options)
                });
            } else {
                // Enhanced error message for Pinterest
                const platform = this.detectPlatformFromUrl(downloadInfo.options.url);
                let errorMessage = error || 'Download failed';

                if (platform === 'pinterest') {
                    // Provide Pinterest-specific guidance
                    errorMessage += '\n\nPinterest Download Tips:\n';
                    errorMessage += '• Ensure the pin is publicly accessible\n';
                    errorMessage += '• Some pins may require authentication\n';
                    errorMessage += '• Try again in a few minutes if rate limited\n';
                    errorMessage += '• Check if the pin contains downloadable video content';
                }

                this.emit('download-error', {
                    id: downloadId,
                    error: errorMessage,
                    platform: platform
                });
            }

            this.activeDownloads.delete(downloadId);
        });

        process.on('error', (err) => {
            this.emit('download-error', {
                id: downloadId,
                error: err.message
            });
            this.activeDownloads.delete(downloadId);
        });

        // Emit download started
        setTimeout(() => {
            this.emit('download-started', {
                id: downloadId,
                size: 0 // Will be updated when we get progress info
            });
        }, 100);
    }

    parseProgress(downloadId, text) {
        // Parse yt-dlp progress output
        const lines = text.split('\n');

        for (const line of lines) {
            // Debug: log the line to see what we're getting
            if (line.includes('[download]')) {
                console.log('Progress line:', line);
            }

            // Multiple patterns to match different yt-dlp output formats
            let progressMatch = null;
            let progress = 0;
            let sizeStr = '';
            let speedStr = '';

            // Pattern 1: [download] 45.2% of 123.45MiB at 2.34MiB/s ETA 00:30
            progressMatch = line.match(/\[download\]\s+(\d+\.?\d*)%\s+of\s+(\d+\.?\d*\w+)\s+at\s+(\d+\.?\d*\w+\/s)/);
            if (progressMatch) {
                progress = parseFloat(progressMatch[1]);
                sizeStr = progressMatch[2];
                speedStr = progressMatch[3];
            } else {
                // Pattern 2: [download] 45.2% of ~123.45MiB at 2.34MiB/s ETA 00:30
                progressMatch = line.match(/\[download\]\s+(\d+\.?\d*)%\s+of\s+~(\d+\.?\d*\w+)\s+at\s+(\d+\.?\d*\w+\/s)/);
                if (progressMatch) {
                    progress = parseFloat(progressMatch[1]);
                    sizeStr = progressMatch[2];
                    speedStr = progressMatch[3];
                } else {
                    // Pattern 3: [download] Downloaded 123456789 of 987654321 bytes (12.5%) at 2.34MiB/s
                    progressMatch = line.match(/\[download\]\s+Downloaded\s+(\d+)\s+of\s+(\d+)\s+bytes\s+\((\d+\.?\d*)%\)\s+at\s+(\d+\.?\d*\w+\/s)/);
                    if (progressMatch) {
                        const downloaded = parseInt(progressMatch[1]);
                        const total = parseInt(progressMatch[2]);
                        progress = parseFloat(progressMatch[3]);
                        sizeStr = `${(total / (1024 * 1024)).toFixed(2)}MiB`;
                        speedStr = progressMatch[4];
                    } else {
                        // Pattern 4: Simple percentage without size info
                        progressMatch = line.match(/\[download\]\s+(\d+\.?\d*)%/);
                        if (progressMatch) {
                            progress = parseFloat(progressMatch[1]);
                            // Use existing size if available
                            const downloadInfo = this.activeDownloads.get(downloadId);
                            if (downloadInfo && downloadInfo.lastSize) {
                                sizeStr = `${(downloadInfo.lastSize / (1024 * 1024)).toFixed(2)}MiB`;
                            }
                        }
                    }
                }
            }

            if (progressMatch && progress > 0) {
                const size = sizeStr ? this.parseSize(sizeStr) : 0;
                const speed = speedStr ? this.parseSpeed(speedStr) : 0;
                const downloaded = size > 0 ? (progress / 100) * size : 0;

                // Store last known size for future reference
                const downloadInfo = this.activeDownloads.get(downloadId);
                if (downloadInfo && size > 0) {
                    downloadInfo.lastSize = size;
                }

                this.emit('download-progress', {
                    id: downloadId,
                    progress: Math.min(progress, 100), // Ensure progress doesn't exceed 100%
                    speed: speed,
                    downloaded: downloaded,
                    size: size
                });
            }

            // Also check for completion messages
            if (line.includes('[download] 100%') || line.includes('has already been downloaded')) {
                this.emit('download-progress', {
                    id: downloadId,
                    progress: 100,
                    speed: 0,
                    downloaded: 0,
                    size: 0
                });
            }
        }
    }

    parseSize(sizeStr) {
        if (!sizeStr) return 0;

        const match = sizeStr.match(/(\d+\.?\d*)(\w+)/);
        if (!match) return 0;

        const value = parseFloat(match[1]);
        const unit = match[2].toLowerCase();

        const multipliers = {
            'b': 1,
            'byte': 1,
            'bytes': 1,
            'k': 1024,
            'kb': 1024,
            'kib': 1024,
            'kilobyte': 1024,
            'kilobytes': 1024,
            'm': 1024 * 1024,
            'mb': 1024 * 1024,
            'mib': 1024 * 1024,
            'megabyte': 1024 * 1024,
            'megabytes': 1024 * 1024,
            'g': 1024 * 1024 * 1024,
            'gb': 1024 * 1024 * 1024,
            'gib': 1024 * 1024 * 1024,
            'gigabyte': 1024 * 1024 * 1024,
            'gigabytes': 1024 * 1024 * 1024,
            't': 1024 * 1024 * 1024 * 1024,
            'tb': 1024 * 1024 * 1024 * 1024,
            'tib': 1024 * 1024 * 1024 * 1024
        };

        return Math.round(value * (multipliers[unit] || 1));
    }

    parseSpeed(speedStr) {
        if (!speedStr) return 0;

        const match = speedStr.match(/(\d+\.?\d*)(\w+)\/s/);
        if (!match) return 0;

        const value = parseFloat(match[1]);
        const unit = match[2].toLowerCase();

        const multipliers = {
            'b': 1,
            'byte': 1,
            'bytes': 1,
            'k': 1024,
            'kb': 1024,
            'kib': 1024,
            'kilobyte': 1024,
            'kilobytes': 1024,
            'm': 1024 * 1024,
            'mb': 1024 * 1024,
            'mib': 1024 * 1024,
            'megabyte': 1024 * 1024,
            'megabytes': 1024 * 1024,
            'g': 1024 * 1024 * 1024,
            'gb': 1024 * 1024 * 1024,
            'gib': 1024 * 1024 * 1024,
            'gigabyte': 1024 * 1024 * 1024,
            'gigabytes': 1024 * 1024 * 1024
        };

        return Math.round(value * (multipliers[unit] || 1));
    }

    getOutputFilePath(options) {
        // This is a simplified version - in reality, we'd need to parse the actual filename
        // from yt-dlp output or use the template to predict it
        return path.join(options.destination, 'downloaded-file.mp4');
    }

    pauseDownload(downloadId) {
        const downloadInfo = this.activeDownloads.get(downloadId);
        if (downloadInfo && downloadInfo.process && !downloadInfo.paused) {
            try {
                // yt-dlp doesn't support pausing, so we'll kill and mark as paused
                downloadInfo.process.kill('SIGTERM');
                downloadInfo.paused = true;
                downloadInfo.pausedAt = Date.now();

                // Emit paused event
                this.emit('download-paused', {
                    id: downloadId
                });

                return true;
            } catch (error) {
                console.error('Error pausing download:', error);
                return false;
            }
        }
        return false;
    }

    async resumeDownload(downloadId) {
        const downloadInfo = this.activeDownloads.get(downloadId);
        if (downloadInfo && downloadInfo.paused) {
            try {
                // Remove the paused flag
                downloadInfo.paused = false;
                delete downloadInfo.pausedAt;

                // Restart the download process
                const args = this.buildDownloadArgs(downloadInfo.options);
                const process = spawn(this.ytDlpPath, args, {
                    stdio: 'pipe',
                    cwd: downloadInfo.options.destination
                });

                // Update the process reference
                downloadInfo.process = process;

                // Setup handlers for the new process
                this.setupProcessHandlers(downloadId, process);

                // Emit resumed event
                this.emit('download-resumed', {
                    id: downloadId
                });

                return true;
            } catch (error) {
                console.error('Error resuming download:', error);
                this.emit('download-error', {
                    id: downloadId,
                    error: `Failed to resume download: ${error.message}`
                });
                return false;
            }
        }
        return false;
    }

    cancelDownload(downloadId) {
        const downloadInfo = this.activeDownloads.get(downloadId);
        if (downloadInfo) {
            try {
                if (downloadInfo.process && !downloadInfo.process.killed) {
                    downloadInfo.process.kill('SIGTERM');
                }

                // Emit cancelled event
                this.emit('download-cancelled', {
                    id: downloadId
                });

                this.activeDownloads.delete(downloadId);
                return true;
            } catch (error) {
                console.error('Error cancelling download:', error);
                this.activeDownloads.delete(downloadId);
                return true; // Still consider it cancelled even if there was an error
            }
        }
        return false;
    }

    getAllActiveDownloads() {
        return Array.from(this.activeDownloads.keys());
    }
}

module.exports = DownloadEngine;
