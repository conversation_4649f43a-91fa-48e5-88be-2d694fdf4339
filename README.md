# Downloader Pro

A modern, intuitive desktop application for downloading content (video and audio) from various platforms including YouTube, Facebook, Instagram, TikTok, Twitter, and Pinterest.

![Downloader Pro](assets/icon.svg)

## Features

### 🎯 Core Functionality
- **Multi-platform support**: Download from YouTube, Facebook, Instagram, TikTok, Twitter, Pinterest, and more
- **Multiple formats**: Video (MP4, MKV, WebM) and Audio (MP3, AAC, OGG)
- **Quality selection**: From 8K UHD down to 240p, plus audio-only options
- **Batch downloads**: Queue multiple downloads with progress tracking
- **Smart clipboard detection**: Automatically detect video URLs when copied

### 🎨 User Interface
- **Modern design**: Clean, minimalist interface with light/dark themes
- **Responsive layout**: Adapts to different window sizes
- **Real-time progress**: Live download progress with speed and ETA
- **Platform integration**: Direct search within supported platforms
- **Accessibility**: Full keyboard navigation and screen reader support

### ⚙️ Advanced Features
- **Download management**: Pause, resume, and cancel downloads
- **Custom destinations**: Choose download folders per file type
- **Subtitle support**: Download subtitles in multiple languages
- **Audio extraction**: Extract audio tracks from videos
- **Format conversion**: Convert between different video/audio formats
- **Auto-updates**: Keep the download engine updated automatically

## Installation

### Prerequisites
- **Node.js** (v16 or higher)
- **yt-dlp** (for full functionality)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-username/downloader-pro.git
   cd downloader-pro
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Install yt-dlp** (required for downloads)
   ```bash
   # Windows (using pip)
   pip install yt-dlp
   
   # macOS (using Homebrew)
   brew install yt-dlp
   
   # Linux (using pip)
   pip install yt-dlp
   ```

4. **Run the application**
   ```bash
   npm start
   ```

### Development Mode
```bash
npm run dev
```

## Building for Distribution

### Build for all platforms
```bash
npm run build
```

### Platform-specific builds
```bash
# Windows
npm run build:win

# macOS
npm run build:mac

# Linux
npm run build:linux
```

## Usage

### Basic Download
1. **Paste URL**: Copy a video URL and paste it into the input field
2. **Configure options**: Select quality, format, and destination
3. **Start download**: Click the download button to begin

### Advanced Features

#### Auto-clipboard Detection
Enable in settings to automatically detect video URLs when copied to clipboard.

#### Custom Download Locations
- **Downloads**: Default downloads folder
- **Videos**: System videos folder
- **Custom**: Choose any folder

#### Quality Selection
- **Best**: Highest available quality
- **Specific**: Choose from 8K, 4K, 2K, 1080p, 720p, 480p, 360p, 240p
- **Audio Only**: Extract audio in various formats

#### Batch Downloads
Queue multiple downloads and manage them from the downloads panel.

#### Pinterest Downloads
Pinterest integration includes special features:

- **Automatic Organization**: Downloads are organized into subfolders:
  - `Pinterest/Pins/` - Individual video pins
  - `Pinterest/Boards/username/boardname/` - Board collections
  - `Pinterest/Profiles/username/` - User profile content
- **Rate Limiting**: Built-in delays to prevent being blocked by Pinterest
- **Metadata Preservation**: Downloads include JSON metadata, descriptions, and thumbnails
- **Error Handling**: User-friendly messages for private pins, deleted content, and authentication issues

**Supported Pinterest URLs:**
- Individual pins: `https://pinterest.com/pin/123456789/`
- Short URLs: `https://pin.it/abc123`
- Boards: `https://pinterest.com/username/board-name/`
- Profiles: `https://pinterest.com/username/`

## Supported Platforms

- **YouTube** (youtube.com, youtu.be)
- **Facebook** (facebook.com, fb.watch)
- **Instagram** (instagram.com)
- **TikTok** (tiktok.com)
- **Twitter/X** (twitter.com, x.com)
- **Pinterest** (pinterest.com, pin.it)
  - Individual pins with video content
  - Board collections
  - User profiles
  - Automatic subfolder organization
  - Rate limiting protection
- **And many more** (via yt-dlp support)

## Configuration

### Settings Location
- **Windows**: `%APPDATA%/downloader-pro/settings.json`
- **macOS**: `~/Library/Application Support/downloader-pro/settings.json`
- **Linux**: `~/.config/downloader-pro/settings.json`

### Available Settings
```json
{
  "theme": "light|dark|system",
  "autoClipboard": true,
  "defaultQuality": "best",
  "defaultFormat": "auto",
  "defaultDestination": "downloads",
  "maxConcurrent": 3,
  "downloadSubtitles": false,
  "subtitleLanguage": "en"
}
```

## Testing

### Quick Test
Run the built-in test script to verify everything is working:
```bash
node test-app.js
```

This will check:
- File structure integrity
- Utility function functionality
- Asset availability
- Common configuration issues

### Manual Testing
1. **Start the application**: `npm start`
2. **Test URL input**: Paste a YouTube URL
3. **Test download**: Try downloading a video
4. **Test settings**: Open settings and change theme
5. **Test platform icons**: Click platform icons to open websites

## Troubleshooting

### Common Issues

#### "yt-dlp not found"
Make sure yt-dlp is installed and available in your system PATH:
```bash
yt-dlp --version
```

If not installed, run:
```bash
npm run install-ytdlp
```

#### Download fails
1. Check if the URL is supported
2. Try updating yt-dlp: `pip install -U yt-dlp`
3. Check your internet connection
4. Some platforms may require authentication

#### Pinterest-specific issues
- **Private pins**: Some pins require authentication or are private
- **Rate limiting**: Wait a few minutes if you get rate limit errors
- **No video content**: Not all pins contain downloadable video
- **Authentication required**: Some content may need Pinterest login
- **Deleted pins**: Pins may have been removed by the user

#### Performance issues
- Reduce concurrent downloads in settings
- Close other bandwidth-intensive applications
- Check available disk space

#### Missing icons for builds
Generate platform-specific icons:
```bash
node assets/generate-icons.js
```

### Getting Help
- **Issues**: [GitHub Issues](https://github.com/your-username/downloader-pro/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-username/downloader-pro/discussions)
- **Documentation**: [Wiki](https://github.com/your-username/downloader-pro/wiki)

## Development

### Project Structure
```
downloader-pro/
├── src/
│   ├── main.js              # Main Electron process
│   ├── preload.js           # Preload script
│   ├── main/
│   │   └── download-engine.js # Download engine
│   └── renderer/
│       ├── index.html       # Main UI
│       ├── styles/          # CSS files
│       └── js/              # Frontend JavaScript
├── assets/                  # Icons and images
├── package.json
└── README.md
```

### Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Code Style
- Use ESLint configuration
- Follow Electron security best practices
- Write descriptive commit messages

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- **yt-dlp**: The powerful download engine that makes this possible
- **Electron**: For the cross-platform desktop framework
- **Contributors**: Thanks to all contributors who help improve this project

## Legal Notice

This software is for personal use only. Please respect the terms of service of the platforms you download from and ensure you have the right to download the content. The developers are not responsible for any misuse of this software.

---

**Made with ❤️ for the open source community**
