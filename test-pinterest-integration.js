#!/usr/bin/env node

/**
 * Pinterest Integration Test Suite
 * Tests all Pinterest functionality including URL validation, platform detection,
 * download logic, error handling, and subfolder organization.
 */

const URLValidator = require('./src/utils/url-validator.js');
const DownloadEngine = require('./src/main/download-engine.js');

class PinterestIntegrationTest {
    constructor() {
        this.urlValidator = new URLValidator();
        this.downloadEngine = new DownloadEngine();
        this.testResults = [];
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️';
        console.log(`${prefix} [${timestamp}] ${message}`);
        
        this.testResults.push({
            timestamp,
            type,
            message
        });
    }

    async runAllTests() {
        this.log('Starting Pinterest Integration Test Suite', 'info');
        console.log('='.repeat(60));

        try {
            await this.testUrlValidation();
            await this.testPlatformDetection();
            await this.testSubfolderOrganization();
            await this.testDownloadArguments();
            await this.testErrorHandling();
            await this.testAppJsIntegration();
            
            this.printSummary();
        } catch (error) {
            this.log(`Test suite failed: ${error.message}`, 'error');
            process.exit(1);
        }
    }

    async testUrlValidation() {
        this.log('Testing URL Validation...', 'info');
        
        const testUrls = [
            // Valid Pinterest URLs
            { url: 'https://pinterest.com/pin/123456789/', expected: true },
            { url: 'https://www.pinterest.com/pin/987654321/', expected: true },
            { url: 'https://pin.it/abc123', expected: true },
            { url: 'https://pinterest.com/user/board/', expected: true },
            { url: 'https://www.pinterest.com/user/', expected: true },
            { url: 'https://pinterest.co.uk/pin/123456789/', expected: true },
            { url: 'https://pinterest.fr/user/board/', expected: true },
            
            // Invalid URLs
            { url: 'https://youtube.com/watch?v=test', expected: false },
            { url: 'https://example.com/pin/123', expected: false },
            { url: 'not-a-url', expected: false }
        ];

        let passed = 0;
        let failed = 0;

        for (const test of testUrls) {
            const platform = this.urlValidator.getSupportedPlatform(test.url);
            const isSupported = this.urlValidator.isSupportedUrl(test.url);
            const isPinterest = platform === 'pinterest';
            
            if ((test.expected && isPinterest && isSupported) || (!test.expected && !isPinterest)) {
                this.log(`✓ URL validation passed: ${test.url}`, 'success');
                passed++;
            } else {
                this.log(`✗ URL validation failed: ${test.url} (expected: ${test.expected}, got: ${isPinterest})`, 'error');
                failed++;
            }
        }

        this.log(`URL Validation Results: ${passed} passed, ${failed} failed`, failed > 0 ? 'error' : 'success');
    }

    async testPlatformDetection() {
        this.log('Testing Platform Detection...', 'info');
        
        const testUrls = [
            { url: 'https://pinterest.com/pin/123456789/', expected: 'pinterest' },
            { url: 'https://pin.it/abc123', expected: 'pinterest' },
            { url: 'https://youtube.com/watch?v=test', expected: 'youtube' },
            { url: 'https://example.com/test', expected: 'unknown' }
        ];

        let passed = 0;
        let failed = 0;

        for (const test of testUrls) {
            const detected = this.downloadEngine.detectPlatformFromUrl(test.url);
            
            if (detected === test.expected) {
                this.log(`✓ Platform detection passed: ${test.url} -> ${detected}`, 'success');
                passed++;
            } else {
                this.log(`✗ Platform detection failed: ${test.url} (expected: ${test.expected}, got: ${detected})`, 'error');
                failed++;
            }
        }

        this.log(`Platform Detection Results: ${passed} passed, ${failed} failed`, failed > 0 ? 'error' : 'success');
    }

    async testSubfolderOrganization() {
        this.log('Testing Subfolder Organization...', 'info');
        
        const testUrls = [
            { 
                url: 'https://pinterest.com/pin/123456789/', 
                expected: 'Pinterest/Pins/%(uploader)s - %(title)s.%(ext)s' 
            },
            { 
                url: 'https://pin.it/abc123', 
                expected: 'Pinterest/Pins/%(uploader)s - %(title)s.%(ext)s' 
            },
            { 
                url: 'https://pinterest.com/user/board-name/', 
                expected: 'Pinterest/Boards/user/board-name/%(title)s.%(ext)s' 
            },
            { 
                url: 'https://pinterest.com/username/', 
                expected: 'Pinterest/Profiles/username/%(title)s.%(ext)s' 
            }
        ];

        let passed = 0;
        let failed = 0;

        for (const test of testUrls) {
            const path = this.downloadEngine.buildPinterestOutputPath(test.url);
            
            if (path === test.expected) {
                this.log(`✓ Subfolder organization passed: ${test.url}`, 'success');
                passed++;
            } else {
                this.log(`✗ Subfolder organization failed: ${test.url}`, 'error');
                this.log(`  Expected: ${test.expected}`, 'error');
                this.log(`  Got: ${path}`, 'error');
                failed++;
            }
        }

        this.log(`Subfolder Organization Results: ${passed} passed, ${failed} failed`, failed > 0 ? 'error' : 'success');
    }

    async testDownloadArguments() {
        this.log('Testing Download Arguments...', 'info');
        
        const pinterestOptions = {
            url: 'https://pinterest.com/pin/123456789/',
            type: 'video',
            quality: 'best',
            format: 'mp4',
            destination: './downloads'
        };

        const args = this.downloadEngine.buildDownloadArgs(pinterestOptions);
        
        // Check for Pinterest-specific arguments
        const requiredArgs = [
            '--sleep-interval',
            '--max-sleep-interval',
            '--write-info-json',
            '--write-description',
            '--write-thumbnail',
            '--ignore-errors',
            '--no-abort-on-error'
        ];

        let passed = 0;
        let failed = 0;

        for (const requiredArg of requiredArgs) {
            if (args.includes(requiredArg)) {
                this.log(`✓ Required argument present: ${requiredArg}`, 'success');
                passed++;
            } else {
                this.log(`✗ Required argument missing: ${requiredArg}`, 'error');
                failed++;
            }
        }

        // Check output path
        const outputIndex = args.indexOf('-o');
        if (outputIndex !== -1 && args[outputIndex + 1].includes('Pinterest/Pins/')) {
            this.log('✓ Pinterest output path configured correctly', 'success');
            passed++;
        } else {
            this.log('✗ Pinterest output path not configured correctly', 'error');
            failed++;
        }

        this.log(`Download Arguments Results: ${passed} passed, ${failed} failed`, failed > 0 ? 'error' : 'success');
    }

    async testErrorHandling() {
        this.log('Testing Error Handling...', 'info');
        
        const errorTests = [
            {
                error: 'ERROR: This pin is private and not available',
                expectedType: 'private_content'
            },
            {
                error: 'ERROR: 404 Not Found - Pin has been removed',
                expectedType: 'content_not_found'
            },
            {
                error: 'ERROR: Rate limit exceeded - too many requests',
                expectedType: 'rate_limited'
            },
            {
                error: 'ERROR: Some other error',
                expectedType: null
            }
        ];

        let passed = 0;
        let failed = 0;

        for (const test of errorTests) {
            const result = this.downloadEngine.detectPinterestError(test.error, 'https://pinterest.com/pin/123456789/');
            
            if ((test.expectedType && result && result.type === test.expectedType) || 
                (!test.expectedType && !result)) {
                this.log(`✓ Error handling passed: ${test.expectedType || 'no error'}`, 'success');
                passed++;
            } else {
                this.log(`✗ Error handling failed for: ${test.error}`, 'error');
                failed++;
            }
        }

        this.log(`Error Handling Results: ${passed} passed, ${failed} failed`, failed > 0 ? 'error' : 'success');
    }

    async testAppJsIntegration() {
        this.log('Testing App.js Integration...', 'info');
        
        // This would require loading the app.js file and testing the methods
        // For now, we'll just verify the files exist and log success
        const fs = require('fs');
        
        try {
            const appJsContent = fs.readFileSync('./src/renderer/js/app.js', 'utf8');
            
            const checks = [
                { name: 'Pinterest platform detection', pattern: /pinterest\.com.*pin\.it/ },
                { name: 'Pinterest platform suggestions', pattern: /Pinterest.*quality.*720p/ },
                { name: 'Pinterest search integration', pattern: /pinterest:[\s\S]*searchUrl.*pinterest\.com/ }
            ];

            let passed = 0;
            let failed = 0;

            for (const check of checks) {
                if (check.pattern.test(appJsContent)) {
                    this.log(`✓ ${check.name} integration found`, 'success');
                    passed++;
                } else {
                    this.log(`✗ ${check.name} integration missing`, 'error');
                    failed++;
                }
            }

            this.log(`App.js Integration Results: ${passed} passed, ${failed} failed`, failed > 0 ? 'error' : 'success');
        } catch (error) {
            this.log(`App.js Integration test failed: ${error.message}`, 'error');
        }
    }

    printSummary() {
        console.log('\n' + '='.repeat(60));
        this.log('Pinterest Integration Test Suite Complete', 'info');
        
        const successCount = this.testResults.filter(r => r.type === 'success').length;
        const errorCount = this.testResults.filter(r => r.type === 'error').length;
        const totalTests = successCount + errorCount;
        
        console.log(`\nSummary:`);
        console.log(`✅ Passed: ${successCount}`);
        console.log(`❌ Failed: ${errorCount}`);
        console.log(`📊 Total: ${totalTests}`);
        console.log(`📈 Success Rate: ${((successCount / totalTests) * 100).toFixed(1)}%`);
        
        if (errorCount === 0) {
            this.log('🎉 All Pinterest integration tests passed!', 'success');
        } else {
            this.log(`⚠️ ${errorCount} tests failed. Please review the errors above.`, 'error');
        }
    }
}

// Run the tests
if (require.main === module) {
    const tester = new PinterestIntegrationTest();
    tester.runAllTests().catch(error => {
        console.error('Test suite crashed:', error);
        process.exit(1);
    });
}

module.exports = PinterestIntegrationTest;
